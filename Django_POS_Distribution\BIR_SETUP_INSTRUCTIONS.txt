BIR COMPLIANCE SETUP INSTRUCTIONS
==================================

IMPORTANT: Before using this POS system for business transactions,
you MUST configure BIR (Bureau of Internal Revenue) compliance information
to avoid violations and penalties.

QUICK SETUP:
-----------
1. Start the POS system
2. Open command prompt in the POS folder
3. Run: python manage.py setup_bir --interactive
4. Follow the prompts to enter your BIR information

REQUIRED INFORMATION:
--------------------
You will need the following information from your BIR documents:

1. Business Name (as registered with BIR)
2. Business Address (complete address)
3. TIN Number (Tax Identification Number)
   Format: XXX-XXX-XXX-XXX
4. ATP Number (Authority to Print)
   Example: ATP123456789
5. ATP Date Issued (when ATP was granted)
   Format: YYYY-MM-DD
6. ATP Valid Until (expiration date)
   Format: YYYY-MM-DD
7. Receipt Series Range
   From: 0000001
   To: 9999999

MANUAL SETUP:
------------
If you prefer to update manually, edit these files:

1. pos/templates/sales/create.html
   - Replace [Your TIN Number] with your actual TIN
   - Replace [ATP Number] with your ATP number
   - Replace [ATP Date] with your ATP issue date

2. pos/static/js/receipt.js
   - Same replacements as above

WHAT'S INCLUDED:
---------------
The POS system now includes BIR-compliant receipt text:

✓ "This receipt shall be valid for five (5) years from the date of the ATP issuance"
✓ "THIS DOCUMENT IS NOT VALID FOR CLAIM OF INPUT TAX"
✓ TIN number display
✓ ATP number and date
✓ Proper receipt formatting

LEGAL REQUIREMENTS:
------------------
Before using this system:

1. Register your business with BIR
2. Obtain Certificate of Registration (COR)
3. Apply for Authority to Print (ATP)
4. Register your books of accounts
5. Configure this POS system with your BIR information

PENALTIES TO AVOID:
------------------
Using receipts without proper BIR compliance can result in:
- ₱1,000 - ₱50,000 for using receipts without ATP
- ₱500 - ₱25,000 for incorrect TIN display
- ₱1,000 - ₱50,000 for missing compliance text

TESTING:
-------
After setup:
1. Process a test sale
2. Print the receipt
3. Verify all BIR information appears correctly
4. Check that compliance text is present

SUPPORT:
-------
For BIR compliance questions, consult with:
- BIR-accredited tax professional
- Certified Public Accountant (CPA)
- Tax lawyer

For technical support with this POS system:
- Check that templates are updated correctly
- Verify receipt printing works properly
- Test all BIR information displays correctly

DISCLAIMER:
----------
This software provides tools to help with BIR compliance but does not
guarantee legal compliance. Users are responsible for ensuring their
business operations comply with all applicable laws and regulations.

Consult with qualified tax professionals for legal advice.
