Django POS System - Professional Distribution
============================================

Thank you for downloading Django POS System!

INSTALLATION INSTRUCTIONS:
==========================

Option 1: Automatic Installation (Recommended)
----------------------------------------------
1. Right-click "install.bat" and select "Run as administrator"
2. Follow the on-screen instructions
3. A desktop shortcut will be created automatically
4. Double-click the desktop shortcut to start the POS system

Option 2: Manual Installation
-----------------------------
1. Copy "Django_POS_System.exe" to any folder on your computer
2. Double-click "Django_POS_System.exe" to run the POS system
3. The system will start automatically and open in your browser

HOW TO USE:
===========

1. Double-click the desktop shortcut "Django POS System"
   OR
   Double-click "Django_POS_System.exe"

2. A window will open with a "Start POS System" button

3. Click "Start POS System" and wait for it to load

4. Your web browser will automatically open with the POS system

5. You can now use the Point of Sale system:
   - Add products and categories
   - Process sales and transactions
   - Generate receipts
   - View sales reports
   - Manage inventory

6. To stop the system, click "Stop System" in the application window

FEATURES:
=========

- Complete Point of Sale system
- Product and inventory management
- Sales processing and receipts
- Category management
- Sales reporting and analytics
- User-friendly interface
- Automatic browser integration
- Professional desktop application

SYSTEM REQUIREMENTS:
===================

- Windows 10 or later
- 4GB RAM minimum
- 500MB free disk space
- Internet browser (Chrome, Firefox, Edge, etc.)

TROUBLESHOOTING:
===============

Problem: Application won't start
Solution: Try running as administrator

Problem: Browser doesn't open automatically
Solution: Manually open your browser and go to http://127.0.0.1:8000

Problem: "Port already in use" error
Solution: Close the application and restart it

Problem: Application appears to freeze
Solution: Wait a few moments for the system to fully load

SUPPORT:
========

If you encounter any issues:
1. Check that you have administrator privileges
2. Ensure no antivirus software is blocking the application
3. Try restarting your computer
4. Contact your system administrator for assistance

UNINSTALLATION:
==============

To remove Django POS System:
1. Delete the desktop shortcut
2. Delete the installation folder (usually in your user directory)
3. The application leaves no registry entries or system files

Thank you for using Django POS System!
=====================================

This is a professional Point of Sale solution designed for
small to medium businesses. Enjoy the convenience of a
desktop application with the power of modern web technology.

Version: 1.0.0
Built with: Django + Python + PyInstaller
