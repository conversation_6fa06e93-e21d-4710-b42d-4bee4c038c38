
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named _scproxy - imported by urllib.request (conditional)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), http.server (delayed, optional), netrc (delayed, conditional), getpass (delayed, optional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named 'collections.abc' - imported by traceback (top-level), inspect (top-level), logging (top-level), typing (top-level), importlib.resources.readers (top-level), selectors (top-level), tracemalloc (top-level), django.utils.hashable (top-level), asyncio.base_events (top-level), http.client (top-level), asyncio.coroutines (top-level), typing_extensions (top-level), django.utils.datastructures (top-level), django.db.models.fields (top-level), django.core.checks.registry (top-level), configparser (top-level), django.db.models.sql.query (top-level), django.template.library (top-level), django.utils.choices (top-level), PIL.Image (top-level), PIL._typing (top-level), xml.etree.ElementTree (top-level), PIL.ImageFilter (top-level), PIL.ImagePalette (top-level), PIL.TiffImagePlugin (top-level), PIL.ImageOps (top-level), PIL.PngImagePlugin (top-level), django.core.paginator (top-level), django.db.migrations.serializer (top-level), django.test.client (top-level), django.contrib.auth.models (top-level), django.db.backends.sqlite3.base (top-level), sqlite3.dbapi2 (top-level), django.template.defaulttags (top-level), PIL.Jpeg2KImagePlugin (top-level), PIL.IptcImagePlugin (top-level)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional)
missing module named posix - imported by posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional), os (conditional, optional)
missing module named resource - imported by posix (top-level)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named numpy - imported by PIL._typing (conditional, optional), django.contrib.gis.shortcuts (optional)
missing module named defusedxml - imported by PIL.Image (optional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named _typeshed - imported by asgiref.sync (conditional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named redis - imported by django.core.cache.backends.redis (delayed)
missing module named pymemcache - imported by django.core.cache.backends.memcached (delayed)
missing module named pylibmc - imported by django.core.cache.backends.memcached (delayed)
missing module named fcntl - imported by subprocess (optional), django.core.files.locks (conditional, optional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named readline - imported by code (delayed, conditional, optional), django.core.management.commands.shell (delayed, optional), rlcompleter (optional), cmd (delayed, conditional, optional), pdb (delayed, optional), sqlite3.__main__ (delayed, conditional, optional)
missing module named psycopg_pool - imported by django.db.backends.postgresql.base (delayed, conditional, optional)
missing module named 'psycopg.types' - imported by django.db.backends.postgresql.psycopg_any (optional), django.db.backends.postgresql.operations (conditional), django.contrib.gis.db.backends.postgis.base (conditional), django.contrib.postgres.signals (conditional)
missing module named 'psycopg2.extras' - imported by django.db.backends.postgresql.psycopg_any (optional), django.db.backends.postgresql.base (conditional), django.contrib.postgres.signals (conditional)
missing module named 'psycopg2.extensions' - imported by django.db.backends.postgresql.base (conditional), django.contrib.gis.db.backends.postgis.adapter (delayed)
missing module named 'psycopg.pq' - imported by django.db.backends.postgresql.base (conditional), django.contrib.gis.db.backends.postgis.base (conditional)
missing module named 'psycopg.postgres' - imported by django.db.backends.postgresql.psycopg_any (optional)
missing module named psycopg2 - imported by django.db.backends.postgresql.base (optional), django.db.backends.postgresql.psycopg_any (optional), django.contrib.postgres.signals (conditional)
missing module named psycopg - imported by django.db.backends.postgresql.base (conditional, optional), django.db.backends.postgresql.psycopg_any (optional)
missing module named cx_Oracle - imported by django.db.backends.oracle.oracledb_any (optional)
missing module named oracledb - imported by django.db.backends.oracle.oracledb_any (optional)
missing module named 'MySQLdb.converters' - imported by django.db.backends.mysql.base (top-level)
missing module named 'MySQLdb.constants' - imported by django.db.backends.mysql.base (top-level), django.db.backends.mysql.introspection (top-level), django.contrib.gis.db.backends.mysql.introspection (top-level)
missing module named MySQLdb - imported by django.db.backends.mysql.base (optional)
missing module named jinja2 - imported by django.template.backends.jinja2 (top-level), django.test.utils (optional)
missing module named django.db.models.Max - imported by django.db.models (top-level), django.db.models.base (top-level)
missing module named django.db.models.IntegerField - imported by django.db.models (top-level), django.db.models.base (top-level), django.contrib.gis.db.models.functions (top-level), django.contrib.postgres.fields.array (top-level), pos.views (top-level)
missing module named django.db.models.NOT_PROVIDED - imported by django.db.models (top-level), django.db.models.base (top-level), django.db.migrations.operations.fields (top-level), django.db.migrations.state (top-level), django.db.migrations.questioner (top-level), django.db.backends.base.schema (top-level), django.db.backends.mysql.schema (top-level), django.db.backends.sqlite3.schema (top-level)
missing module named django.db.models.Field - imported by django.db.models (top-level), django.db.models.query (top-level), django.forms.models (delayed), django.contrib.admin.views.main (top-level), django.contrib.gis.db.models.fields (top-level), django.contrib.postgres.search (top-level), django.contrib.postgres.fields.array (top-level), django.contrib.postgres.fields.hstore (top-level)
missing module named django.db.models.DateTimeField - imported by django.db.models (top-level), django.db.models.query (top-level), django.contrib.postgres.functions (top-level)
missing module named django.db.models.DateField - imported by django.db.models (top-level), django.db.models.query (top-level)
missing module named django.db.models.DurationField - imported by django.db.models (top-level), django.db.backends.oracle.functions (top-level)
missing module named django.db.models.DecimalField - imported by django.db.models (top-level), django.db.backends.oracle.functions (top-level)
missing module named django.db.models.BooleanField - imported by django.db.models (delayed), django.db.models.query_utils (delayed), django.db.models.sql.where (delayed), django.contrib.gis.db.models.functions (top-level)
missing module named django.db.models.UniqueConstraint - imported by django.db.models (top-level), django.db.models.options (top-level), django.db.backends.mysql.schema (top-level), django.db.backends.sqlite3.schema (top-level)
missing module named django.db.models.AutoField - imported by django.db.models (top-level), django.db.models.options (top-level), django.db.models.query (top-level), django.forms.models (delayed), django.db.backends.oracle.operations (top-level)
missing module named pywatchman - imported by django.utils.autoreload (optional)
missing module named termios - imported by django.utils.autoreload (optional), getpass (optional), tty (top-level), _pyrepl.pager (delayed, optional)
missing module named pyimod02_importers - imported by C:\Users\<USER>\djangoproject\pos_system\.venv\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
missing module named tblib - imported by django.test.runner (optional)
missing module named ipdb - imported by django.test.runner (optional)
missing module named bpython - imported by django.core.management.commands.shell (delayed)
missing module named selenium - imported by django.test.selenium (delayed, conditional)
missing module named yaml - imported by django.core.serializers.pyyaml (top-level)
missing module named geoip2 - imported by django.contrib.gis.geoip2 (optional)
missing module named 'psycopg.adapt' - imported by django.contrib.gis.db.backends.postgis.base (conditional)
missing module named 'docutils.parsers' - imported by django.contrib.admindocs.utils (optional)
missing module named 'docutils.nodes' - imported by django.contrib.admindocs.utils (optional)
missing module named docutils - imported by django.contrib.admindocs.utils (optional)
missing module named 'selenium.webdriver' - imported by django.contrib.admin.tests (delayed)
missing module named _suggestions - imported by traceback (delayed, optional)
