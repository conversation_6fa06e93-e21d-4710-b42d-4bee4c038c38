# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['pos_launcher.py'],
    pathex=[],
    binaries=[],
    datas=[('pos', 'pos'), ('pos_system', 'pos_system'), ('static', 'static'), ('manage.py', '.'), ('desktop_app_settings.py', '.')],
    hiddenimports=['django', 'django.contrib.admin', 'django.contrib.auth', 'django.contrib.contenttypes', 'django.contrib.sessions', 'django.contrib.messages', 'django.contrib.staticfiles', 'django.contrib.humanize', 'pos', 'pos.models', 'pos.views', 'pos.urls', 'tkinter'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='Django_POS_System',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='NONE',
)
