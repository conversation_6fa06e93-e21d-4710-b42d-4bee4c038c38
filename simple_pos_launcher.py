#!/usr/bin/env python3
"""
Simple Django POS Launcher - Avoids PyConfig issues
This is a minimal launcher that works reliably.
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def main():
    """Simple main function that avoids complex imports"""
    print("=" * 50)
    print("    Django POS System")
    print("=" * 50)
    print()
    
    # Get the directory where this script is located
    if getattr(sys, 'frozen', False):
        # Running as compiled executable
        project_dir = Path(sys.executable).parent
    else:
        # Running as Python script
        project_dir = Path(__file__).resolve().parent
    
    print(f"Project directory: {project_dir}")
    
    # Set Django settings
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pos_system.settings')
    
    try:
        print("Starting Django server...")
        print("Please wait...")
        
        # Start Django server
        server_process = subprocess.Popen([
            sys.executable, 'manage.py', 'runserver', '127.0.0.1:8000', '--noreload'
        ], cwd=project_dir, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait for server to start
        print("Waiting for server to start...")
        time.sleep(5)
        
        # Check if server is running
        if server_process.poll() is None:
            print("✅ Server started successfully!")
            print("🌐 Opening browser...")
            
            # Open browser
            webbrowser.open('http://127.0.0.1:8000')
            
            print()
            print("=" * 50)
            print("🎉 POS System is now running!")
            print("📍 URL: http://127.0.0.1:8000")
            print("⚠️  Keep this window open while using the POS system")
            print("❌ Close this window to stop the POS system")
            print("=" * 50)
            print()
            
            # Wait for user to close or Ctrl+C
            try:
                server_process.wait()
            except KeyboardInterrupt:
                print("\n🛑 Stopping POS system...")
                server_process.terminate()
                try:
                    server_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    server_process.kill()
                print("✅ POS system stopped.")
        else:
            print("❌ Failed to start server")
            stdout, stderr = server_process.communicate()
            print(f"Error: {stderr.decode()}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\nPress Enter to exit...")
    input()

if __name__ == "__main__":
    main()
