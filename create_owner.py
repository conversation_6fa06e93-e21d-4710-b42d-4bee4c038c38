#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to create an owner account for the POS system.
Run this script to create a superuser account that can access the dashboard.
"""

import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pos_system.settings')
django.setup()

from django.contrib.auth.models import User
from django.core.exceptions import ValidationError

def create_owner():
    print("=== POS System Owner Account Creation ===\n")
    
    # Check if any superuser already exists
    if User.objects.filter(is_superuser=True).exists():
        print("⚠️  A superuser already exists in the system.")
        existing_users = User.objects.filter(is_superuser=True)
        print("Existing superusers:")
        for user in existing_users:
            print(f"   - {user.username}")
        
        choice = input("\nDo you want to create another owner account? (y/N): ").lower()
        if choice not in ['y', 'yes']:
            print("Operation cancelled.")
            return
    
    print("Please provide the following information for the owner account:\n")
    
    # Get username
    while True:
        username = input("Username: ").strip()
        if not username:
            print("❌ Username cannot be empty. Please try again.")
            continue
        
        if User.objects.filter(username=username).exists():
            print(f"❌ Username '{username}' already exists. Please choose a different username.")
            continue
        
        if len(username) < 3:
            print("❌ Username must be at least 3 characters long.")
            continue
        
        break
    
    # Get email (optional)
    email = input("Email (optional): ").strip()
    if email and '@' not in email:
        print("⚠️  Invalid email format. Proceeding without email.")
        email = ""
    
    # Get password
    while True:
        password = input("Password: ").strip()
        if not password:
            print("❌ Password cannot be empty. Please try again.")
            continue
        
        if len(password) < 6:
            print("❌ Password must be at least 6 characters long.")
            continue
        
        password_confirm = input("Confirm password: ").strip()
        if password != password_confirm:
            print("❌ Passwords do not match. Please try again.")
            continue
        
        break
    
    # Create the user
    try:
        user = User.objects.create_superuser(
            username=username,
            email=email if email else '',
            password=password
        )
        
        print(f"\n✅ Owner account '{username}' created successfully!")
        print("\n📋 Account Details:")
        print(f"   Username: {username}")
        print(f"   Email: {email if email else 'Not provided'}")
        print(f"   Superuser: Yes")
        print(f"   Staff: Yes")
        
        print("\n🔗 Access Information:")
        print("   Dashboard URL: http://localhost:8000/dashboard/")
        print("   Login URL: http://localhost:8000/login/")
        
        print("\n💡 Next Steps:")
        print("   1. Start the Django server: python manage.py runserver")
        print("   2. Visit the login page and use your credentials")
        print("   3. Access the dashboard to manage your POS system")
        
    except ValidationError as e:
        print(f"❌ Error creating user: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    try:
        create_owner()
    except KeyboardInterrupt:
        print("\n\nOperation cancelled by user.")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("Make sure you're running this script from the project root directory.")
