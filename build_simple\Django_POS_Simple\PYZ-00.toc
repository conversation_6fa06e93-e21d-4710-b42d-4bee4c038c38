('C:\\Users\\<USER>\\djangoproject\\pos_system\\build_simple\\Django_POS_Simple\\PYZ-00.pyz',
 [('PIL',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_colorize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_colorize.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_compression.py',
   'PYMODULE'),
  ('_ios_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_ios_support.py',
   'PYMODULE'),
  ('_markupbase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_markupbase.py',
   'PYMODULE'),
  ('_opcode_metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_opcode_metadata.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydatetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyrepl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\__init__.py',
   'PYMODULE'),
  ('_pyrepl.pager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\pager.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\argparse.py',
   'PYMODULE'),
  ('asgiref',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\asgiref\\__init__.py',
   'PYMODULE'),
  ('asgiref.current_thread_executor',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\asgiref\\current_thread_executor.py',
   'PYMODULE'),
  ('asgiref.local',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\asgiref\\local.py',
   'PYMODULE'),
  ('asgiref.sync',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\asgiref\\sync.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\base64.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bdb.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bisect.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\calendar.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\cmd.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\codeop.py',
   'PYMODULE'),
  ('colorama',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\colorsys.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\copy.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\datetime.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\difflib.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\dis.py',
   'PYMODULE'),
  ('django',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\__init__.py',
   'PYMODULE'),
  ('django.__main__',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\__main__.py',
   'PYMODULE'),
  ('django.apps',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\apps\\__init__.py',
   'PYMODULE'),
  ('django.apps.config',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\apps\\config.py',
   'PYMODULE'),
  ('django.apps.registry',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\apps\\registry.py',
   'PYMODULE'),
  ('django.conf',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\__init__.py',
   'PYMODULE'),
  ('django.conf.global_settings',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\global_settings.py',
   'PYMODULE'),
  ('django.conf.locale',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ar',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ar\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ar.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ar\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ar_DZ',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ar_DZ\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ar_DZ.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ar_DZ\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.az',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\az\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.az.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\az\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.bg',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\bg\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.bg.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\bg\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.bn',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\bn\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.bn.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\bn\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.bs',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\bs\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.bs.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\bs\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ca',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ca\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ca.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ca\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ckb',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ckb\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ckb.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ckb\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.cs',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\cs\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.cs.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\cs\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.cy',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\cy\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.cy.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\cy\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.da',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\da\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.da.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\da\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.de',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\de\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.de.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\de\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.de_CH',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\de_CH\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.de_CH.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\de_CH\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.el',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\el\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.el.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\el\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.en',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\en\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.en.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\en\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.en_AU',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\en_AU\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.en_AU.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\en_AU\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.en_CA',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\en_CA\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.en_CA.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\en_CA\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.en_GB',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\en_GB\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.en_GB.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\en_GB\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.en_IE',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\en_IE\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.en_IE.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\en_IE\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.eo',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\eo\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.eo.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\eo\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.es',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\es\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.es.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\es\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.es_AR',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\es_AR\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.es_AR.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\es_AR\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.es_CO',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\es_CO\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.es_CO.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\es_CO\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.es_MX',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\es_MX\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.es_MX.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\es_MX\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.es_NI',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\es_NI\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.es_NI.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\es_NI\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.es_PR',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\es_PR\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.es_PR.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\es_PR\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.et',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\et\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.et.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\et\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.eu',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\eu\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.eu.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\eu\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.fa',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\fa\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.fa.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\fa\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.fi',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\fi\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.fi.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\fi\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.fr',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\fr\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.fr.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\fr\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.fr_BE',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\fr_BE\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.fr_BE.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\fr_BE\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.fr_CA',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\fr_CA\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.fr_CA.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\fr_CA\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.fr_CH',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\fr_CH\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.fr_CH.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\fr_CH\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.fy',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\fy\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.fy.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\fy\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ga',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ga\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ga.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ga\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.gd',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\gd\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.gd.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\gd\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.gl',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\gl\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.gl.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\gl\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.he',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\he\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.he.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\he\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.hi',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\hi\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.hi.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\hi\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.hr',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\hr\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.hr.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\hr\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.hu',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\hu\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.hu.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\hu\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.id',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\id\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.id.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\id\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ig',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ig\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ig.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ig\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.is',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\is\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.is.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\is\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.it',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\it\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.it.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\it\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ja',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ja\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ja.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ja\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ka',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ka\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ka.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ka\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.km',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\km\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.km.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\km\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.kn',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\kn\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.kn.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\kn\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ko',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ko\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ko.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ko\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ky',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ky\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ky.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ky\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.lt',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\lt\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.lt.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\lt\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.lv',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\lv\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.lv.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\lv\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.mk',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\mk\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.mk.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\mk\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ml',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ml\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ml.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ml\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.mn',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\mn\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.mn.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\mn\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ms',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ms\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ms.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ms\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.nb',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\nb\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.nb.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\nb\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.nl',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\nl\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.nl.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\nl\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.nn',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\nn\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.nn.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\nn\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.pl',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\pl\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.pl.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\pl\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.pt',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\pt\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.pt.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\pt\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.pt_BR',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\pt_BR\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.pt_BR.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\pt_BR\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ro',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ro\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ro.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ro\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ru',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ru\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ru.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ru\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.sk',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\sk\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.sk.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\sk\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.sl',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\sl\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.sl.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\sl\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.sq',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\sq\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.sq.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\sq\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.sr',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\sr\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.sr.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\sr\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.sr_Latn',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\sr_Latn\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.sr_Latn.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\sr_Latn\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.sv',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\sv\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.sv.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\sv\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ta',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ta\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ta.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ta\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.te',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\te\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.te.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\te\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.tg',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\tg\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.tg.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\tg\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.th',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\th\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.th.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\th\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.tk',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\tk\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.tk.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\tk\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.tr',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\tr\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.tr.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\tr\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.ug',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ug\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.ug.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\ug\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.uk',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\uk\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.uk.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\uk\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.uz',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\uz\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.uz.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\uz\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.vi',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\vi\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.vi.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\vi\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.zh_Hans',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\zh_Hans\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.zh_Hans.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\zh_Hans\\formats.py',
   'PYMODULE'),
  ('django.conf.locale.zh_Hant',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\zh_Hant\\__init__.py',
   'PYMODULE'),
  ('django.conf.locale.zh_Hant.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\locale\\zh_Hant\\formats.py',
   'PYMODULE'),
  ('django.conf.urls',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\urls\\__init__.py',
   'PYMODULE'),
  ('django.conf.urls.i18n',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\urls\\i18n.py',
   'PYMODULE'),
  ('django.conf.urls.static',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\conf\\urls\\static.py',
   'PYMODULE'),
  ('django.contrib',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\__init__.py',
   'PYMODULE'),
  ('django.contrib.admin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\__init__.py',
   'PYMODULE'),
  ('django.contrib.admin.actions',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\actions.py',
   'PYMODULE'),
  ('django.contrib.admin.apps',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\apps.py',
   'PYMODULE'),
  ('django.contrib.admin.checks',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\checks.py',
   'PYMODULE'),
  ('django.contrib.admin.decorators',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\decorators.py',
   'PYMODULE'),
  ('django.contrib.admin.exceptions',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\exceptions.py',
   'PYMODULE'),
  ('django.contrib.admin.filters',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\filters.py',
   'PYMODULE'),
  ('django.contrib.admin.forms',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\forms.py',
   'PYMODULE'),
  ('django.contrib.admin.helpers',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\helpers.py',
   'PYMODULE'),
  ('django.contrib.admin.migrations',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\migrations\\__init__.py',
   'PYMODULE'),
  ('django.contrib.admin.migrations.0001_initial',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\migrations\\0001_initial.py',
   'PYMODULE'),
  ('django.contrib.admin.migrations.0002_logentry_remove_auto_add',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\migrations\\0002_logentry_remove_auto_add.py',
   'PYMODULE'),
  ('django.contrib.admin.migrations.0003_logentry_add_action_flag_choices',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\migrations\\0003_logentry_add_action_flag_choices.py',
   'PYMODULE'),
  ('django.contrib.admin.models',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\models.py',
   'PYMODULE'),
  ('django.contrib.admin.options',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\options.py',
   'PYMODULE'),
  ('django.contrib.admin.sites',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\sites.py',
   'PYMODULE'),
  ('django.contrib.admin.templatetags',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\templatetags\\__init__.py',
   'PYMODULE'),
  ('django.contrib.admin.templatetags.admin_list',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\templatetags\\admin_list.py',
   'PYMODULE'),
  ('django.contrib.admin.templatetags.admin_modify',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\templatetags\\admin_modify.py',
   'PYMODULE'),
  ('django.contrib.admin.templatetags.admin_urls',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\templatetags\\admin_urls.py',
   'PYMODULE'),
  ('django.contrib.admin.templatetags.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\templatetags\\base.py',
   'PYMODULE'),
  ('django.contrib.admin.templatetags.log',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\templatetags\\log.py',
   'PYMODULE'),
  ('django.contrib.admin.tests',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\tests.py',
   'PYMODULE'),
  ('django.contrib.admin.utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\utils.py',
   'PYMODULE'),
  ('django.contrib.admin.views',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\views\\__init__.py',
   'PYMODULE'),
  ('django.contrib.admin.views.autocomplete',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\views\\autocomplete.py',
   'PYMODULE'),
  ('django.contrib.admin.views.decorators',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\views\\decorators.py',
   'PYMODULE'),
  ('django.contrib.admin.views.main',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\views\\main.py',
   'PYMODULE'),
  ('django.contrib.admin.widgets',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admin\\widgets.py',
   'PYMODULE'),
  ('django.contrib.admindocs',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admindocs\\__init__.py',
   'PYMODULE'),
  ('django.contrib.admindocs.apps',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admindocs\\apps.py',
   'PYMODULE'),
  ('django.contrib.admindocs.middleware',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admindocs\\middleware.py',
   'PYMODULE'),
  ('django.contrib.admindocs.urls',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admindocs\\urls.py',
   'PYMODULE'),
  ('django.contrib.admindocs.utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admindocs\\utils.py',
   'PYMODULE'),
  ('django.contrib.admindocs.views',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\admindocs\\views.py',
   'PYMODULE'),
  ('django.contrib.auth',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\__init__.py',
   'PYMODULE'),
  ('django.contrib.auth.admin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\admin.py',
   'PYMODULE'),
  ('django.contrib.auth.apps',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\apps.py',
   'PYMODULE'),
  ('django.contrib.auth.backends',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\backends.py',
   'PYMODULE'),
  ('django.contrib.auth.base_user',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\base_user.py',
   'PYMODULE'),
  ('django.contrib.auth.checks',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\checks.py',
   'PYMODULE'),
  ('django.contrib.auth.context_processors',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\context_processors.py',
   'PYMODULE'),
  ('django.contrib.auth.decorators',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\decorators.py',
   'PYMODULE'),
  ('django.contrib.auth.forms',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\forms.py',
   'PYMODULE'),
  ('django.contrib.auth.handlers',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\handlers\\__init__.py',
   'PYMODULE'),
  ('django.contrib.auth.handlers.modwsgi',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\handlers\\modwsgi.py',
   'PYMODULE'),
  ('django.contrib.auth.hashers',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\hashers.py',
   'PYMODULE'),
  ('django.contrib.auth.management',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\management\\__init__.py',
   'PYMODULE'),
  ('django.contrib.auth.management.commands',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\management\\commands\\__init__.py',
   'PYMODULE'),
  ('django.contrib.auth.management.commands.changepassword',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\management\\commands\\changepassword.py',
   'PYMODULE'),
  ('django.contrib.auth.management.commands.createsuperuser',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\management\\commands\\createsuperuser.py',
   'PYMODULE'),
  ('django.contrib.auth.middleware',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\middleware.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\migrations\\__init__.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations.0001_initial',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\migrations\\0001_initial.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations.0002_alter_permission_name_max_length',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\migrations\\0002_alter_permission_name_max_length.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations.0003_alter_user_email_max_length',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\migrations\\0003_alter_user_email_max_length.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations.0004_alter_user_username_opts',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\migrations\\0004_alter_user_username_opts.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations.0005_alter_user_last_login_null',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\migrations\\0005_alter_user_last_login_null.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations.0006_require_contenttypes_0002',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\migrations\\0006_require_contenttypes_0002.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations.0007_alter_validators_add_error_messages',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\migrations\\0007_alter_validators_add_error_messages.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations.0008_alter_user_username_max_length',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\migrations\\0008_alter_user_username_max_length.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations.0009_alter_user_last_name_max_length',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\migrations\\0009_alter_user_last_name_max_length.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations.0010_alter_group_name_max_length',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\migrations\\0010_alter_group_name_max_length.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations.0011_update_proxy_permissions',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\migrations\\0011_update_proxy_permissions.py',
   'PYMODULE'),
  ('django.contrib.auth.migrations.0012_alter_user_first_name_max_length',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\migrations\\0012_alter_user_first_name_max_length.py',
   'PYMODULE'),
  ('django.contrib.auth.mixins',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\mixins.py',
   'PYMODULE'),
  ('django.contrib.auth.models',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\models.py',
   'PYMODULE'),
  ('django.contrib.auth.password_validation',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\password_validation.py',
   'PYMODULE'),
  ('django.contrib.auth.signals',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\signals.py',
   'PYMODULE'),
  ('django.contrib.auth.tokens',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\tokens.py',
   'PYMODULE'),
  ('django.contrib.auth.urls',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\urls.py',
   'PYMODULE'),
  ('django.contrib.auth.validators',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\validators.py',
   'PYMODULE'),
  ('django.contrib.auth.views',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\auth\\views.py',
   'PYMODULE'),
  ('django.contrib.contenttypes',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\contenttypes\\__init__.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.admin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\contenttypes\\admin.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.apps',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\contenttypes\\apps.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.checks',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\contenttypes\\checks.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.fields',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\contenttypes\\fields.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.forms',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\contenttypes\\forms.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.management',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\contenttypes\\management\\__init__.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.management.commands',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\contenttypes\\management\\commands\\__init__.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.management.commands.remove_stale_contenttypes',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\contenttypes\\management\\commands\\remove_stale_contenttypes.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.migrations',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\contenttypes\\migrations\\__init__.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.migrations.0001_initial',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\contenttypes\\migrations\\0001_initial.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.migrations.0002_remove_content_type_name',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\contenttypes\\migrations\\0002_remove_content_type_name.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.models',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\contenttypes\\models.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.prefetch',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\contenttypes\\prefetch.py',
   'PYMODULE'),
  ('django.contrib.contenttypes.views',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\contenttypes\\views.py',
   'PYMODULE'),
  ('django.contrib.flatpages',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\flatpages\\__init__.py',
   'PYMODULE'),
  ('django.contrib.flatpages.admin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\flatpages\\admin.py',
   'PYMODULE'),
  ('django.contrib.flatpages.apps',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\flatpages\\apps.py',
   'PYMODULE'),
  ('django.contrib.flatpages.forms',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\flatpages\\forms.py',
   'PYMODULE'),
  ('django.contrib.flatpages.middleware',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\flatpages\\middleware.py',
   'PYMODULE'),
  ('django.contrib.flatpages.migrations',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\flatpages\\migrations\\__init__.py',
   'PYMODULE'),
  ('django.contrib.flatpages.migrations.0001_initial',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\flatpages\\migrations\\0001_initial.py',
   'PYMODULE'),
  ('django.contrib.flatpages.models',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\flatpages\\models.py',
   'PYMODULE'),
  ('django.contrib.flatpages.sitemaps',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\flatpages\\sitemaps.py',
   'PYMODULE'),
  ('django.contrib.flatpages.templatetags',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\flatpages\\templatetags\\__init__.py',
   'PYMODULE'),
  ('django.contrib.flatpages.templatetags.flatpages',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\flatpages\\templatetags\\flatpages.py',
   'PYMODULE'),
  ('django.contrib.flatpages.urls',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\flatpages\\urls.py',
   'PYMODULE'),
  ('django.contrib.flatpages.views',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\flatpages\\views.py',
   'PYMODULE'),
  ('django.contrib.gis',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.apps',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\apps.py',
   'PYMODULE'),
  ('django.contrib.gis.db',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\base\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.base.adapter',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\base\\adapter.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.base.features',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\base\\features.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.base.models',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\base\\models.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.base.operations',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\base\\operations.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.mysql',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\mysql\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.mysql.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\mysql\\base.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.mysql.features',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\mysql\\features.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.mysql.introspection',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\mysql\\introspection.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.mysql.operations',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\mysql\\operations.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.mysql.schema',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\mysql\\schema.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.oracle',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\oracle\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.oracle.adapter',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\oracle\\adapter.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.oracle.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\oracle\\base.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.oracle.features',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\oracle\\features.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.oracle.introspection',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\oracle\\introspection.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.oracle.models',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\oracle\\models.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.oracle.operations',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\oracle\\operations.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.oracle.schema',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\oracle\\schema.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.postgis',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\postgis\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.postgis.adapter',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\postgis\\adapter.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.postgis.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\postgis\\base.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.postgis.const',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\postgis\\const.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.postgis.features',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\postgis\\features.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.postgis.introspection',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\postgis\\introspection.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.postgis.models',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\postgis\\models.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.postgis.operations',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\postgis\\operations.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.postgis.pgraster',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\postgis\\pgraster.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.postgis.schema',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\postgis\\schema.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.spatialite',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\spatialite\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.spatialite.adapter',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\spatialite\\adapter.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.spatialite.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\spatialite\\base.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.spatialite.client',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\spatialite\\client.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.spatialite.features',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\spatialite\\features.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.spatialite.introspection',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\spatialite\\introspection.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.spatialite.models',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\spatialite\\models.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.spatialite.operations',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\spatialite\\operations.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.spatialite.schema',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\spatialite\\schema.py',
   'PYMODULE'),
  ('django.contrib.gis.db.backends.utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\backends\\utils.py',
   'PYMODULE'),
  ('django.contrib.gis.db.models',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\models\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.db.models.aggregates',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\models\\aggregates.py',
   'PYMODULE'),
  ('django.contrib.gis.db.models.fields',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\models\\fields.py',
   'PYMODULE'),
  ('django.contrib.gis.db.models.functions',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\models\\functions.py',
   'PYMODULE'),
  ('django.contrib.gis.db.models.lookups',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\models\\lookups.py',
   'PYMODULE'),
  ('django.contrib.gis.db.models.proxy',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\models\\proxy.py',
   'PYMODULE'),
  ('django.contrib.gis.db.models.sql',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\models\\sql\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.db.models.sql.conversion',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\db\\models\\sql\\conversion.py',
   'PYMODULE'),
  ('django.contrib.gis.feeds',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\feeds.py',
   'PYMODULE'),
  ('django.contrib.gis.forms',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\forms\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.forms.fields',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\forms\\fields.py',
   'PYMODULE'),
  ('django.contrib.gis.forms.widgets',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\forms\\widgets.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\gdal\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\gdal\\base.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.datasource',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\gdal\\datasource.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.driver',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\gdal\\driver.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.envelope',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\gdal\\envelope.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.error',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\gdal\\error.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.feature',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\gdal\\feature.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.field',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\gdal\\field.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.geometries',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\gdal\\geometries.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.geomtype',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\gdal\\geomtype.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.layer',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\gdal\\layer.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.libgdal',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\gdal\\libgdal.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.prototypes',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\gdal\\prototypes\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.prototypes.ds',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\gdal\\prototypes\\ds.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.prototypes.errcheck',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\gdal\\prototypes\\errcheck.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.prototypes.generation',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\gdal\\prototypes\\generation.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.prototypes.geom',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\gdal\\prototypes\\geom.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.prototypes.raster',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\gdal\\prototypes\\raster.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.prototypes.srs',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\gdal\\prototypes\\srs.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.raster',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\gdal\\raster\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.raster.band',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\gdal\\raster\\band.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.raster.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\gdal\\raster\\base.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.raster.const',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\gdal\\raster\\const.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.raster.source',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\gdal\\raster\\source.py',
   'PYMODULE'),
  ('django.contrib.gis.gdal.srs',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\gdal\\srs.py',
   'PYMODULE'),
  ('django.contrib.gis.geoip2',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geoip2.py',
   'PYMODULE'),
  ('django.contrib.gis.geometry',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geometry.py',
   'PYMODULE'),
  ('django.contrib.gis.geos',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geos\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geos\\base.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.collections',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geos\\collections.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.coordseq',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geos\\coordseq.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.error',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geos\\error.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.factory',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geos\\factory.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.geometry',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geos\\geometry.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.io',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geos\\io.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.libgeos',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geos\\libgeos.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.linestring',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geos\\linestring.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.mutable_list',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geos\\mutable_list.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.point',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geos\\point.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.polygon',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geos\\polygon.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.prepared',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geos\\prepared.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.prototypes',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geos\\prototypes\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.prototypes.coordseq',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geos\\prototypes\\coordseq.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.prototypes.errcheck',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geos\\prototypes\\errcheck.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.prototypes.geom',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geos\\prototypes\\geom.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.prototypes.io',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geos\\prototypes\\io.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.prototypes.misc',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geos\\prototypes\\misc.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.prototypes.predicates',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geos\\prototypes\\predicates.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.prototypes.prepared',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geos\\prototypes\\prepared.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.prototypes.threadsafe',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geos\\prototypes\\threadsafe.py',
   'PYMODULE'),
  ('django.contrib.gis.geos.prototypes.topology',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\geos\\prototypes\\topology.py',
   'PYMODULE'),
  ('django.contrib.gis.management',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\management\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.management.commands',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\management\\commands\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.management.commands.inspectdb',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\management\\commands\\inspectdb.py',
   'PYMODULE'),
  ('django.contrib.gis.management.commands.ogrinspect',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\management\\commands\\ogrinspect.py',
   'PYMODULE'),
  ('django.contrib.gis.measure',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\measure.py',
   'PYMODULE'),
  ('django.contrib.gis.ptr',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\ptr.py',
   'PYMODULE'),
  ('django.contrib.gis.serializers',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\serializers\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.serializers.geojson',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\serializers\\geojson.py',
   'PYMODULE'),
  ('django.contrib.gis.shortcuts',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\shortcuts.py',
   'PYMODULE'),
  ('django.contrib.gis.utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\utils\\__init__.py',
   'PYMODULE'),
  ('django.contrib.gis.utils.layermapping',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\utils\\layermapping.py',
   'PYMODULE'),
  ('django.contrib.gis.utils.ogrinfo',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\utils\\ogrinfo.py',
   'PYMODULE'),
  ('django.contrib.gis.utils.ogrinspect',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\utils\\ogrinspect.py',
   'PYMODULE'),
  ('django.contrib.gis.utils.srs',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\utils\\srs.py',
   'PYMODULE'),
  ('django.contrib.gis.views',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\gis\\views.py',
   'PYMODULE'),
  ('django.contrib.humanize',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\humanize\\__init__.py',
   'PYMODULE'),
  ('django.contrib.humanize.apps',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\humanize\\apps.py',
   'PYMODULE'),
  ('django.contrib.humanize.templatetags',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\humanize\\templatetags\\__init__.py',
   'PYMODULE'),
  ('django.contrib.humanize.templatetags.humanize',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\humanize\\templatetags\\humanize.py',
   'PYMODULE'),
  ('django.contrib.messages',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\messages\\__init__.py',
   'PYMODULE'),
  ('django.contrib.messages.api',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\messages\\api.py',
   'PYMODULE'),
  ('django.contrib.messages.apps',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\messages\\apps.py',
   'PYMODULE'),
  ('django.contrib.messages.constants',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\messages\\constants.py',
   'PYMODULE'),
  ('django.contrib.messages.context_processors',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\messages\\context_processors.py',
   'PYMODULE'),
  ('django.contrib.messages.middleware',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\messages\\middleware.py',
   'PYMODULE'),
  ('django.contrib.messages.storage',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\messages\\storage\\__init__.py',
   'PYMODULE'),
  ('django.contrib.messages.storage.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\messages\\storage\\base.py',
   'PYMODULE'),
  ('django.contrib.messages.storage.cookie',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\messages\\storage\\cookie.py',
   'PYMODULE'),
  ('django.contrib.messages.storage.fallback',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\messages\\storage\\fallback.py',
   'PYMODULE'),
  ('django.contrib.messages.storage.session',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\messages\\storage\\session.py',
   'PYMODULE'),
  ('django.contrib.messages.test',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\messages\\test.py',
   'PYMODULE'),
  ('django.contrib.messages.utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\messages\\utils.py',
   'PYMODULE'),
  ('django.contrib.messages.views',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\messages\\views.py',
   'PYMODULE'),
  ('django.contrib.postgres',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\postgres\\__init__.py',
   'PYMODULE'),
  ('django.contrib.postgres.apps',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\postgres\\apps.py',
   'PYMODULE'),
  ('django.contrib.postgres.constraints',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\postgres\\constraints.py',
   'PYMODULE'),
  ('django.contrib.postgres.expressions',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\postgres\\expressions.py',
   'PYMODULE'),
  ('django.contrib.postgres.fields',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\postgres\\fields\\__init__.py',
   'PYMODULE'),
  ('django.contrib.postgres.fields.array',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\postgres\\fields\\array.py',
   'PYMODULE'),
  ('django.contrib.postgres.fields.citext',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\postgres\\fields\\citext.py',
   'PYMODULE'),
  ('django.contrib.postgres.fields.hstore',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\postgres\\fields\\hstore.py',
   'PYMODULE'),
  ('django.contrib.postgres.fields.jsonb',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\postgres\\fields\\jsonb.py',
   'PYMODULE'),
  ('django.contrib.postgres.fields.ranges',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\postgres\\fields\\ranges.py',
   'PYMODULE'),
  ('django.contrib.postgres.fields.utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\postgres\\fields\\utils.py',
   'PYMODULE'),
  ('django.contrib.postgres.forms',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\postgres\\forms\\__init__.py',
   'PYMODULE'),
  ('django.contrib.postgres.forms.array',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\postgres\\forms\\array.py',
   'PYMODULE'),
  ('django.contrib.postgres.forms.hstore',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\postgres\\forms\\hstore.py',
   'PYMODULE'),
  ('django.contrib.postgres.forms.ranges',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\postgres\\forms\\ranges.py',
   'PYMODULE'),
  ('django.contrib.postgres.functions',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\postgres\\functions.py',
   'PYMODULE'),
  ('django.contrib.postgres.indexes',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\postgres\\indexes.py',
   'PYMODULE'),
  ('django.contrib.postgres.lookups',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\postgres\\lookups.py',
   'PYMODULE'),
  ('django.contrib.postgres.operations',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\postgres\\operations.py',
   'PYMODULE'),
  ('django.contrib.postgres.search',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\postgres\\search.py',
   'PYMODULE'),
  ('django.contrib.postgres.serializers',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\postgres\\serializers.py',
   'PYMODULE'),
  ('django.contrib.postgres.signals',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\postgres\\signals.py',
   'PYMODULE'),
  ('django.contrib.postgres.utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\postgres\\utils.py',
   'PYMODULE'),
  ('django.contrib.postgres.validators',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\postgres\\validators.py',
   'PYMODULE'),
  ('django.contrib.redirects',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\redirects\\__init__.py',
   'PYMODULE'),
  ('django.contrib.redirects.admin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\redirects\\admin.py',
   'PYMODULE'),
  ('django.contrib.redirects.apps',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\redirects\\apps.py',
   'PYMODULE'),
  ('django.contrib.redirects.middleware',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\redirects\\middleware.py',
   'PYMODULE'),
  ('django.contrib.redirects.migrations',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\redirects\\migrations\\__init__.py',
   'PYMODULE'),
  ('django.contrib.redirects.migrations.0001_initial',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\redirects\\migrations\\0001_initial.py',
   'PYMODULE'),
  ('django.contrib.redirects.migrations.0002_alter_redirect_new_path_help_text',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\redirects\\migrations\\0002_alter_redirect_new_path_help_text.py',
   'PYMODULE'),
  ('django.contrib.redirects.models',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\redirects\\models.py',
   'PYMODULE'),
  ('django.contrib.sessions',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sessions\\__init__.py',
   'PYMODULE'),
  ('django.contrib.sessions.apps',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sessions\\apps.py',
   'PYMODULE'),
  ('django.contrib.sessions.backends',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sessions\\backends\\__init__.py',
   'PYMODULE'),
  ('django.contrib.sessions.backends.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sessions\\backends\\base.py',
   'PYMODULE'),
  ('django.contrib.sessions.backends.cache',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sessions\\backends\\cache.py',
   'PYMODULE'),
  ('django.contrib.sessions.backends.cached_db',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sessions\\backends\\cached_db.py',
   'PYMODULE'),
  ('django.contrib.sessions.backends.db',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sessions\\backends\\db.py',
   'PYMODULE'),
  ('django.contrib.sessions.backends.file',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sessions\\backends\\file.py',
   'PYMODULE'),
  ('django.contrib.sessions.backends.signed_cookies',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sessions\\backends\\signed_cookies.py',
   'PYMODULE'),
  ('django.contrib.sessions.base_session',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sessions\\base_session.py',
   'PYMODULE'),
  ('django.contrib.sessions.exceptions',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sessions\\exceptions.py',
   'PYMODULE'),
  ('django.contrib.sessions.management',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sessions\\management\\__init__.py',
   'PYMODULE'),
  ('django.contrib.sessions.management.commands',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sessions\\management\\commands\\__init__.py',
   'PYMODULE'),
  ('django.contrib.sessions.management.commands.clearsessions',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sessions\\management\\commands\\clearsessions.py',
   'PYMODULE'),
  ('django.contrib.sessions.middleware',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sessions\\middleware.py',
   'PYMODULE'),
  ('django.contrib.sessions.migrations',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sessions\\migrations\\__init__.py',
   'PYMODULE'),
  ('django.contrib.sessions.migrations.0001_initial',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sessions\\migrations\\0001_initial.py',
   'PYMODULE'),
  ('django.contrib.sessions.models',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sessions\\models.py',
   'PYMODULE'),
  ('django.contrib.sessions.serializers',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sessions\\serializers.py',
   'PYMODULE'),
  ('django.contrib.sitemaps',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sitemaps\\__init__.py',
   'PYMODULE'),
  ('django.contrib.sitemaps.apps',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sitemaps\\apps.py',
   'PYMODULE'),
  ('django.contrib.sitemaps.views',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sitemaps\\views.py',
   'PYMODULE'),
  ('django.contrib.sites',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sites\\__init__.py',
   'PYMODULE'),
  ('django.contrib.sites.admin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sites\\admin.py',
   'PYMODULE'),
  ('django.contrib.sites.apps',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sites\\apps.py',
   'PYMODULE'),
  ('django.contrib.sites.checks',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sites\\checks.py',
   'PYMODULE'),
  ('django.contrib.sites.management',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sites\\management.py',
   'PYMODULE'),
  ('django.contrib.sites.managers',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sites\\managers.py',
   'PYMODULE'),
  ('django.contrib.sites.middleware',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sites\\middleware.py',
   'PYMODULE'),
  ('django.contrib.sites.migrations',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sites\\migrations\\__init__.py',
   'PYMODULE'),
  ('django.contrib.sites.migrations.0001_initial',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sites\\migrations\\0001_initial.py',
   'PYMODULE'),
  ('django.contrib.sites.migrations.0002_alter_domain_unique',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sites\\migrations\\0002_alter_domain_unique.py',
   'PYMODULE'),
  ('django.contrib.sites.models',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sites\\models.py',
   'PYMODULE'),
  ('django.contrib.sites.requests',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sites\\requests.py',
   'PYMODULE'),
  ('django.contrib.sites.shortcuts',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\sites\\shortcuts.py',
   'PYMODULE'),
  ('django.contrib.staticfiles',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\staticfiles\\__init__.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.apps',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\staticfiles\\apps.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.checks',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\staticfiles\\checks.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.finders',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\staticfiles\\finders.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.handlers',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\staticfiles\\handlers.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.management',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\staticfiles\\management\\__init__.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.management.commands',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\staticfiles\\management\\commands\\__init__.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.management.commands.collectstatic',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\staticfiles\\management\\commands\\collectstatic.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.management.commands.findstatic',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\staticfiles\\management\\commands\\findstatic.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.management.commands.runserver',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\staticfiles\\management\\commands\\runserver.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.storage',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\staticfiles\\storage.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.testing',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\staticfiles\\testing.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.urls',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\staticfiles\\urls.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\staticfiles\\utils.py',
   'PYMODULE'),
  ('django.contrib.staticfiles.views',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\staticfiles\\views.py',
   'PYMODULE'),
  ('django.contrib.syndication',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\syndication\\__init__.py',
   'PYMODULE'),
  ('django.contrib.syndication.apps',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\syndication\\apps.py',
   'PYMODULE'),
  ('django.contrib.syndication.views',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\contrib\\syndication\\views.py',
   'PYMODULE'),
  ('django.core',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\__init__.py',
   'PYMODULE'),
  ('django.core.asgi',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\asgi.py',
   'PYMODULE'),
  ('django.core.cache',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\cache\\__init__.py',
   'PYMODULE'),
  ('django.core.cache.backends',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\cache\\backends\\__init__.py',
   'PYMODULE'),
  ('django.core.cache.backends.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\cache\\backends\\base.py',
   'PYMODULE'),
  ('django.core.cache.backends.db',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\cache\\backends\\db.py',
   'PYMODULE'),
  ('django.core.cache.backends.dummy',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\cache\\backends\\dummy.py',
   'PYMODULE'),
  ('django.core.cache.backends.filebased',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\cache\\backends\\filebased.py',
   'PYMODULE'),
  ('django.core.cache.backends.locmem',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\cache\\backends\\locmem.py',
   'PYMODULE'),
  ('django.core.cache.backends.memcached',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\cache\\backends\\memcached.py',
   'PYMODULE'),
  ('django.core.cache.backends.redis',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\cache\\backends\\redis.py',
   'PYMODULE'),
  ('django.core.cache.utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\cache\\utils.py',
   'PYMODULE'),
  ('django.core.checks',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\checks\\__init__.py',
   'PYMODULE'),
  ('django.core.checks.async_checks',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\checks\\async_checks.py',
   'PYMODULE'),
  ('django.core.checks.caches',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\checks\\caches.py',
   'PYMODULE'),
  ('django.core.checks.compatibility',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\checks\\compatibility\\__init__.py',
   'PYMODULE'),
  ('django.core.checks.compatibility.django_4_0',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\checks\\compatibility\\django_4_0.py',
   'PYMODULE'),
  ('django.core.checks.database',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\checks\\database.py',
   'PYMODULE'),
  ('django.core.checks.files',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\checks\\files.py',
   'PYMODULE'),
  ('django.core.checks.messages',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\checks\\messages.py',
   'PYMODULE'),
  ('django.core.checks.model_checks',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\checks\\model_checks.py',
   'PYMODULE'),
  ('django.core.checks.registry',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\checks\\registry.py',
   'PYMODULE'),
  ('django.core.checks.security',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\checks\\security\\__init__.py',
   'PYMODULE'),
  ('django.core.checks.security.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\checks\\security\\base.py',
   'PYMODULE'),
  ('django.core.checks.security.csrf',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\checks\\security\\csrf.py',
   'PYMODULE'),
  ('django.core.checks.security.sessions',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\checks\\security\\sessions.py',
   'PYMODULE'),
  ('django.core.checks.templates',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\checks\\templates.py',
   'PYMODULE'),
  ('django.core.checks.translation',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\checks\\translation.py',
   'PYMODULE'),
  ('django.core.checks.urls',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\checks\\urls.py',
   'PYMODULE'),
  ('django.core.exceptions',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\exceptions.py',
   'PYMODULE'),
  ('django.core.files',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\files\\__init__.py',
   'PYMODULE'),
  ('django.core.files.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\files\\base.py',
   'PYMODULE'),
  ('django.core.files.images',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\files\\images.py',
   'PYMODULE'),
  ('django.core.files.locks',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\files\\locks.py',
   'PYMODULE'),
  ('django.core.files.move',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\files\\move.py',
   'PYMODULE'),
  ('django.core.files.storage',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\files\\storage\\__init__.py',
   'PYMODULE'),
  ('django.core.files.storage.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\files\\storage\\base.py',
   'PYMODULE'),
  ('django.core.files.storage.filesystem',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\files\\storage\\filesystem.py',
   'PYMODULE'),
  ('django.core.files.storage.handler',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\files\\storage\\handler.py',
   'PYMODULE'),
  ('django.core.files.storage.memory',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\files\\storage\\memory.py',
   'PYMODULE'),
  ('django.core.files.storage.mixins',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\files\\storage\\mixins.py',
   'PYMODULE'),
  ('django.core.files.temp',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\files\\temp.py',
   'PYMODULE'),
  ('django.core.files.uploadedfile',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\files\\uploadedfile.py',
   'PYMODULE'),
  ('django.core.files.uploadhandler',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\files\\uploadhandler.py',
   'PYMODULE'),
  ('django.core.files.utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\files\\utils.py',
   'PYMODULE'),
  ('django.core.handlers',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\handlers\\__init__.py',
   'PYMODULE'),
  ('django.core.handlers.asgi',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\handlers\\asgi.py',
   'PYMODULE'),
  ('django.core.handlers.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\handlers\\base.py',
   'PYMODULE'),
  ('django.core.handlers.exception',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\handlers\\exception.py',
   'PYMODULE'),
  ('django.core.handlers.wsgi',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\handlers\\wsgi.py',
   'PYMODULE'),
  ('django.core.mail',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\mail\\__init__.py',
   'PYMODULE'),
  ('django.core.mail.backends',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\mail\\backends\\__init__.py',
   'PYMODULE'),
  ('django.core.mail.backends.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\mail\\backends\\base.py',
   'PYMODULE'),
  ('django.core.mail.backends.console',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\mail\\backends\\console.py',
   'PYMODULE'),
  ('django.core.mail.backends.dummy',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\mail\\backends\\dummy.py',
   'PYMODULE'),
  ('django.core.mail.backends.filebased',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\mail\\backends\\filebased.py',
   'PYMODULE'),
  ('django.core.mail.backends.locmem',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\mail\\backends\\locmem.py',
   'PYMODULE'),
  ('django.core.mail.backends.smtp',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\mail\\backends\\smtp.py',
   'PYMODULE'),
  ('django.core.mail.message',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\mail\\message.py',
   'PYMODULE'),
  ('django.core.mail.utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\mail\\utils.py',
   'PYMODULE'),
  ('django.core.management',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\__init__.py',
   'PYMODULE'),
  ('django.core.management.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\base.py',
   'PYMODULE'),
  ('django.core.management.color',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\color.py',
   'PYMODULE'),
  ('django.core.management.commands',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\__init__.py',
   'PYMODULE'),
  ('django.core.management.commands.check',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\check.py',
   'PYMODULE'),
  ('django.core.management.commands.compilemessages',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\compilemessages.py',
   'PYMODULE'),
  ('django.core.management.commands.createcachetable',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\createcachetable.py',
   'PYMODULE'),
  ('django.core.management.commands.dbshell',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\dbshell.py',
   'PYMODULE'),
  ('django.core.management.commands.diffsettings',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\diffsettings.py',
   'PYMODULE'),
  ('django.core.management.commands.dumpdata',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\dumpdata.py',
   'PYMODULE'),
  ('django.core.management.commands.flush',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\flush.py',
   'PYMODULE'),
  ('django.core.management.commands.inspectdb',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\inspectdb.py',
   'PYMODULE'),
  ('django.core.management.commands.loaddata',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\loaddata.py',
   'PYMODULE'),
  ('django.core.management.commands.makemessages',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\makemessages.py',
   'PYMODULE'),
  ('django.core.management.commands.makemigrations',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\makemigrations.py',
   'PYMODULE'),
  ('django.core.management.commands.migrate',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\migrate.py',
   'PYMODULE'),
  ('django.core.management.commands.optimizemigration',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\optimizemigration.py',
   'PYMODULE'),
  ('django.core.management.commands.runserver',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\runserver.py',
   'PYMODULE'),
  ('django.core.management.commands.sendtestemail',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\sendtestemail.py',
   'PYMODULE'),
  ('django.core.management.commands.shell',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\shell.py',
   'PYMODULE'),
  ('django.core.management.commands.showmigrations',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\showmigrations.py',
   'PYMODULE'),
  ('django.core.management.commands.sqlflush',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\sqlflush.py',
   'PYMODULE'),
  ('django.core.management.commands.sqlmigrate',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\sqlmigrate.py',
   'PYMODULE'),
  ('django.core.management.commands.sqlsequencereset',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\sqlsequencereset.py',
   'PYMODULE'),
  ('django.core.management.commands.squashmigrations',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\squashmigrations.py',
   'PYMODULE'),
  ('django.core.management.commands.startapp',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\startapp.py',
   'PYMODULE'),
  ('django.core.management.commands.startproject',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\startproject.py',
   'PYMODULE'),
  ('django.core.management.commands.test',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\test.py',
   'PYMODULE'),
  ('django.core.management.commands.testserver',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\commands\\testserver.py',
   'PYMODULE'),
  ('django.core.management.sql',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\sql.py',
   'PYMODULE'),
  ('django.core.management.templates',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\templates.py',
   'PYMODULE'),
  ('django.core.management.utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\management\\utils.py',
   'PYMODULE'),
  ('django.core.paginator',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\paginator.py',
   'PYMODULE'),
  ('django.core.serializers',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\serializers\\__init__.py',
   'PYMODULE'),
  ('django.core.serializers.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\serializers\\base.py',
   'PYMODULE'),
  ('django.core.serializers.json',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\serializers\\json.py',
   'PYMODULE'),
  ('django.core.serializers.jsonl',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\serializers\\jsonl.py',
   'PYMODULE'),
  ('django.core.serializers.python',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\serializers\\python.py',
   'PYMODULE'),
  ('django.core.serializers.pyyaml',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\serializers\\pyyaml.py',
   'PYMODULE'),
  ('django.core.serializers.xml_serializer',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\serializers\\xml_serializer.py',
   'PYMODULE'),
  ('django.core.servers',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\servers\\__init__.py',
   'PYMODULE'),
  ('django.core.servers.basehttp',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\servers\\basehttp.py',
   'PYMODULE'),
  ('django.core.signals',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\signals.py',
   'PYMODULE'),
  ('django.core.signing',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\signing.py',
   'PYMODULE'),
  ('django.core.validators',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\validators.py',
   'PYMODULE'),
  ('django.core.wsgi',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\core\\wsgi.py',
   'PYMODULE'),
  ('django.db',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\__init__.py',
   'PYMODULE'),
  ('django.db.backends',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\__init__.py',
   'PYMODULE'),
  ('django.db.backends.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\base\\__init__.py',
   'PYMODULE'),
  ('django.db.backends.base.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\base\\base.py',
   'PYMODULE'),
  ('django.db.backends.base.client',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\base\\client.py',
   'PYMODULE'),
  ('django.db.backends.base.creation',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\base\\creation.py',
   'PYMODULE'),
  ('django.db.backends.base.features',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\base\\features.py',
   'PYMODULE'),
  ('django.db.backends.base.introspection',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\base\\introspection.py',
   'PYMODULE'),
  ('django.db.backends.base.operations',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\base\\operations.py',
   'PYMODULE'),
  ('django.db.backends.base.schema',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\base\\schema.py',
   'PYMODULE'),
  ('django.db.backends.base.validation',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\base\\validation.py',
   'PYMODULE'),
  ('django.db.backends.ddl_references',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\ddl_references.py',
   'PYMODULE'),
  ('django.db.backends.dummy',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\dummy\\__init__.py',
   'PYMODULE'),
  ('django.db.backends.dummy.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\dummy\\base.py',
   'PYMODULE'),
  ('django.db.backends.dummy.features',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\dummy\\features.py',
   'PYMODULE'),
  ('django.db.backends.mysql',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\mysql\\__init__.py',
   'PYMODULE'),
  ('django.db.backends.mysql.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\mysql\\base.py',
   'PYMODULE'),
  ('django.db.backends.mysql.client',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\mysql\\client.py',
   'PYMODULE'),
  ('django.db.backends.mysql.compiler',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\mysql\\compiler.py',
   'PYMODULE'),
  ('django.db.backends.mysql.creation',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\mysql\\creation.py',
   'PYMODULE'),
  ('django.db.backends.mysql.features',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\mysql\\features.py',
   'PYMODULE'),
  ('django.db.backends.mysql.introspection',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\mysql\\introspection.py',
   'PYMODULE'),
  ('django.db.backends.mysql.operations',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\mysql\\operations.py',
   'PYMODULE'),
  ('django.db.backends.mysql.schema',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\mysql\\schema.py',
   'PYMODULE'),
  ('django.db.backends.mysql.validation',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\mysql\\validation.py',
   'PYMODULE'),
  ('django.db.backends.oracle',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\oracle\\__init__.py',
   'PYMODULE'),
  ('django.db.backends.oracle.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\oracle\\base.py',
   'PYMODULE'),
  ('django.db.backends.oracle.client',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\oracle\\client.py',
   'PYMODULE'),
  ('django.db.backends.oracle.creation',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\oracle\\creation.py',
   'PYMODULE'),
  ('django.db.backends.oracle.features',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\oracle\\features.py',
   'PYMODULE'),
  ('django.db.backends.oracle.functions',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\oracle\\functions.py',
   'PYMODULE'),
  ('django.db.backends.oracle.introspection',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\oracle\\introspection.py',
   'PYMODULE'),
  ('django.db.backends.oracle.operations',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\oracle\\operations.py',
   'PYMODULE'),
  ('django.db.backends.oracle.oracledb_any',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\oracle\\oracledb_any.py',
   'PYMODULE'),
  ('django.db.backends.oracle.schema',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\oracle\\schema.py',
   'PYMODULE'),
  ('django.db.backends.oracle.utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\oracle\\utils.py',
   'PYMODULE'),
  ('django.db.backends.oracle.validation',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\oracle\\validation.py',
   'PYMODULE'),
  ('django.db.backends.postgresql',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\postgresql\\__init__.py',
   'PYMODULE'),
  ('django.db.backends.postgresql.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\postgresql\\base.py',
   'PYMODULE'),
  ('django.db.backends.postgresql.client',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\postgresql\\client.py',
   'PYMODULE'),
  ('django.db.backends.postgresql.creation',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\postgresql\\creation.py',
   'PYMODULE'),
  ('django.db.backends.postgresql.features',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\postgresql\\features.py',
   'PYMODULE'),
  ('django.db.backends.postgresql.introspection',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\postgresql\\introspection.py',
   'PYMODULE'),
  ('django.db.backends.postgresql.operations',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\postgresql\\operations.py',
   'PYMODULE'),
  ('django.db.backends.postgresql.psycopg_any',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\postgresql\\psycopg_any.py',
   'PYMODULE'),
  ('django.db.backends.postgresql.schema',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\postgresql\\schema.py',
   'PYMODULE'),
  ('django.db.backends.signals',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\signals.py',
   'PYMODULE'),
  ('django.db.backends.sqlite3',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('django.db.backends.sqlite3._functions',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\sqlite3\\_functions.py',
   'PYMODULE'),
  ('django.db.backends.sqlite3.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\sqlite3\\base.py',
   'PYMODULE'),
  ('django.db.backends.sqlite3.client',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\sqlite3\\client.py',
   'PYMODULE'),
  ('django.db.backends.sqlite3.creation',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\sqlite3\\creation.py',
   'PYMODULE'),
  ('django.db.backends.sqlite3.features',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\sqlite3\\features.py',
   'PYMODULE'),
  ('django.db.backends.sqlite3.introspection',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\sqlite3\\introspection.py',
   'PYMODULE'),
  ('django.db.backends.sqlite3.operations',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\sqlite3\\operations.py',
   'PYMODULE'),
  ('django.db.backends.sqlite3.schema',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\sqlite3\\schema.py',
   'PYMODULE'),
  ('django.db.backends.utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\backends\\utils.py',
   'PYMODULE'),
  ('django.db.migrations',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\migrations\\__init__.py',
   'PYMODULE'),
  ('django.db.migrations.autodetector',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\migrations\\autodetector.py',
   'PYMODULE'),
  ('django.db.migrations.exceptions',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\migrations\\exceptions.py',
   'PYMODULE'),
  ('django.db.migrations.executor',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\migrations\\executor.py',
   'PYMODULE'),
  ('django.db.migrations.graph',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\migrations\\graph.py',
   'PYMODULE'),
  ('django.db.migrations.loader',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\migrations\\loader.py',
   'PYMODULE'),
  ('django.db.migrations.migration',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\migrations\\migration.py',
   'PYMODULE'),
  ('django.db.migrations.operations',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\migrations\\operations\\__init__.py',
   'PYMODULE'),
  ('django.db.migrations.operations.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\migrations\\operations\\base.py',
   'PYMODULE'),
  ('django.db.migrations.operations.fields',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\migrations\\operations\\fields.py',
   'PYMODULE'),
  ('django.db.migrations.operations.models',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\migrations\\operations\\models.py',
   'PYMODULE'),
  ('django.db.migrations.operations.special',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\migrations\\operations\\special.py',
   'PYMODULE'),
  ('django.db.migrations.optimizer',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\migrations\\optimizer.py',
   'PYMODULE'),
  ('django.db.migrations.questioner',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\migrations\\questioner.py',
   'PYMODULE'),
  ('django.db.migrations.recorder',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\migrations\\recorder.py',
   'PYMODULE'),
  ('django.db.migrations.serializer',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\migrations\\serializer.py',
   'PYMODULE'),
  ('django.db.migrations.state',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\migrations\\state.py',
   'PYMODULE'),
  ('django.db.migrations.utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\migrations\\utils.py',
   'PYMODULE'),
  ('django.db.migrations.writer',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\migrations\\writer.py',
   'PYMODULE'),
  ('django.db.models',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\__init__.py',
   'PYMODULE'),
  ('django.db.models.aggregates',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\aggregates.py',
   'PYMODULE'),
  ('django.db.models.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\base.py',
   'PYMODULE'),
  ('django.db.models.constants',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\constants.py',
   'PYMODULE'),
  ('django.db.models.constraints',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\constraints.py',
   'PYMODULE'),
  ('django.db.models.deletion',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\deletion.py',
   'PYMODULE'),
  ('django.db.models.enums',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\enums.py',
   'PYMODULE'),
  ('django.db.models.expressions',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\expressions.py',
   'PYMODULE'),
  ('django.db.models.fields',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\fields\\__init__.py',
   'PYMODULE'),
  ('django.db.models.fields.files',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\fields\\files.py',
   'PYMODULE'),
  ('django.db.models.fields.generated',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\fields\\generated.py',
   'PYMODULE'),
  ('django.db.models.fields.json',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\fields\\json.py',
   'PYMODULE'),
  ('django.db.models.fields.mixins',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\fields\\mixins.py',
   'PYMODULE'),
  ('django.db.models.fields.proxy',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\fields\\proxy.py',
   'PYMODULE'),
  ('django.db.models.fields.related',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\fields\\related.py',
   'PYMODULE'),
  ('django.db.models.fields.related_descriptors',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\fields\\related_descriptors.py',
   'PYMODULE'),
  ('django.db.models.fields.related_lookups',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\fields\\related_lookups.py',
   'PYMODULE'),
  ('django.db.models.fields.reverse_related',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\fields\\reverse_related.py',
   'PYMODULE'),
  ('django.db.models.functions',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\functions\\__init__.py',
   'PYMODULE'),
  ('django.db.models.functions.comparison',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\functions\\comparison.py',
   'PYMODULE'),
  ('django.db.models.functions.datetime',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\functions\\datetime.py',
   'PYMODULE'),
  ('django.db.models.functions.math',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\functions\\math.py',
   'PYMODULE'),
  ('django.db.models.functions.mixins',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\functions\\mixins.py',
   'PYMODULE'),
  ('django.db.models.functions.text',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\functions\\text.py',
   'PYMODULE'),
  ('django.db.models.functions.window',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\functions\\window.py',
   'PYMODULE'),
  ('django.db.models.indexes',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\indexes.py',
   'PYMODULE'),
  ('django.db.models.lookups',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\lookups.py',
   'PYMODULE'),
  ('django.db.models.manager',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\manager.py',
   'PYMODULE'),
  ('django.db.models.options',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\options.py',
   'PYMODULE'),
  ('django.db.models.query',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\query.py',
   'PYMODULE'),
  ('django.db.models.query_utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\query_utils.py',
   'PYMODULE'),
  ('django.db.models.signals',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\signals.py',
   'PYMODULE'),
  ('django.db.models.sql',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\sql\\__init__.py',
   'PYMODULE'),
  ('django.db.models.sql.compiler',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\sql\\compiler.py',
   'PYMODULE'),
  ('django.db.models.sql.constants',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\sql\\constants.py',
   'PYMODULE'),
  ('django.db.models.sql.datastructures',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\sql\\datastructures.py',
   'PYMODULE'),
  ('django.db.models.sql.query',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\sql\\query.py',
   'PYMODULE'),
  ('django.db.models.sql.subqueries',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\sql\\subqueries.py',
   'PYMODULE'),
  ('django.db.models.sql.where',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\sql\\where.py',
   'PYMODULE'),
  ('django.db.models.utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\models\\utils.py',
   'PYMODULE'),
  ('django.db.transaction',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\transaction.py',
   'PYMODULE'),
  ('django.db.utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\db\\utils.py',
   'PYMODULE'),
  ('django.dispatch',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\dispatch\\__init__.py',
   'PYMODULE'),
  ('django.dispatch.dispatcher',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\dispatch\\dispatcher.py',
   'PYMODULE'),
  ('django.forms',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\forms\\__init__.py',
   'PYMODULE'),
  ('django.forms.boundfield',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\forms\\boundfield.py',
   'PYMODULE'),
  ('django.forms.fields',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\forms\\fields.py',
   'PYMODULE'),
  ('django.forms.forms',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\forms\\forms.py',
   'PYMODULE'),
  ('django.forms.formsets',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\forms\\formsets.py',
   'PYMODULE'),
  ('django.forms.models',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\forms\\models.py',
   'PYMODULE'),
  ('django.forms.renderers',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\forms\\renderers.py',
   'PYMODULE'),
  ('django.forms.utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\forms\\utils.py',
   'PYMODULE'),
  ('django.forms.widgets',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\forms\\widgets.py',
   'PYMODULE'),
  ('django.http',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\http\\__init__.py',
   'PYMODULE'),
  ('django.http.cookie',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\http\\cookie.py',
   'PYMODULE'),
  ('django.http.multipartparser',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\http\\multipartparser.py',
   'PYMODULE'),
  ('django.http.request',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\http\\request.py',
   'PYMODULE'),
  ('django.http.response',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\http\\response.py',
   'PYMODULE'),
  ('django.middleware',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\middleware\\__init__.py',
   'PYMODULE'),
  ('django.middleware.cache',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\middleware\\cache.py',
   'PYMODULE'),
  ('django.middleware.clickjacking',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\middleware\\clickjacking.py',
   'PYMODULE'),
  ('django.middleware.common',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\middleware\\common.py',
   'PYMODULE'),
  ('django.middleware.csrf',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\middleware\\csrf.py',
   'PYMODULE'),
  ('django.middleware.gzip',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\middleware\\gzip.py',
   'PYMODULE'),
  ('django.middleware.http',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\middleware\\http.py',
   'PYMODULE'),
  ('django.middleware.locale',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\middleware\\locale.py',
   'PYMODULE'),
  ('django.middleware.security',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\middleware\\security.py',
   'PYMODULE'),
  ('django.shortcuts',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\shortcuts.py',
   'PYMODULE'),
  ('django.template',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\__init__.py',
   'PYMODULE'),
  ('django.template.autoreload',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\autoreload.py',
   'PYMODULE'),
  ('django.template.backends',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\backends\\__init__.py',
   'PYMODULE'),
  ('django.template.backends.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\backends\\base.py',
   'PYMODULE'),
  ('django.template.backends.django',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\backends\\django.py',
   'PYMODULE'),
  ('django.template.backends.dummy',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\backends\\dummy.py',
   'PYMODULE'),
  ('django.template.backends.jinja2',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\backends\\jinja2.py',
   'PYMODULE'),
  ('django.template.backends.utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\backends\\utils.py',
   'PYMODULE'),
  ('django.template.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\base.py',
   'PYMODULE'),
  ('django.template.context',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\context.py',
   'PYMODULE'),
  ('django.template.context_processors',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\context_processors.py',
   'PYMODULE'),
  ('django.template.defaultfilters',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\defaultfilters.py',
   'PYMODULE'),
  ('django.template.defaulttags',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\defaulttags.py',
   'PYMODULE'),
  ('django.template.engine',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\engine.py',
   'PYMODULE'),
  ('django.template.exceptions',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\exceptions.py',
   'PYMODULE'),
  ('django.template.library',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\library.py',
   'PYMODULE'),
  ('django.template.loader',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\loader.py',
   'PYMODULE'),
  ('django.template.loader_tags',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\loader_tags.py',
   'PYMODULE'),
  ('django.template.loaders',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\loaders\\__init__.py',
   'PYMODULE'),
  ('django.template.loaders.app_directories',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\loaders\\app_directories.py',
   'PYMODULE'),
  ('django.template.loaders.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\loaders\\base.py',
   'PYMODULE'),
  ('django.template.loaders.cached',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\loaders\\cached.py',
   'PYMODULE'),
  ('django.template.loaders.filesystem',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\loaders\\filesystem.py',
   'PYMODULE'),
  ('django.template.loaders.locmem',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\loaders\\locmem.py',
   'PYMODULE'),
  ('django.template.response',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\response.py',
   'PYMODULE'),
  ('django.template.smartif',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\smartif.py',
   'PYMODULE'),
  ('django.template.utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\template\\utils.py',
   'PYMODULE'),
  ('django.templatetags',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\templatetags\\__init__.py',
   'PYMODULE'),
  ('django.templatetags.cache',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\templatetags\\cache.py',
   'PYMODULE'),
  ('django.templatetags.i18n',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\templatetags\\i18n.py',
   'PYMODULE'),
  ('django.templatetags.l10n',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\templatetags\\l10n.py',
   'PYMODULE'),
  ('django.templatetags.static',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\templatetags\\static.py',
   'PYMODULE'),
  ('django.templatetags.tz',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\templatetags\\tz.py',
   'PYMODULE'),
  ('django.test',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\test\\__init__.py',
   'PYMODULE'),
  ('django.test.client',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\test\\client.py',
   'PYMODULE'),
  ('django.test.html',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\test\\html.py',
   'PYMODULE'),
  ('django.test.runner',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\test\\runner.py',
   'PYMODULE'),
  ('django.test.selenium',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\test\\selenium.py',
   'PYMODULE'),
  ('django.test.signals',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\test\\signals.py',
   'PYMODULE'),
  ('django.test.testcases',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\test\\testcases.py',
   'PYMODULE'),
  ('django.test.utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\test\\utils.py',
   'PYMODULE'),
  ('django.urls',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\urls\\__init__.py',
   'PYMODULE'),
  ('django.urls.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\urls\\base.py',
   'PYMODULE'),
  ('django.urls.conf',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\urls\\conf.py',
   'PYMODULE'),
  ('django.urls.converters',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\urls\\converters.py',
   'PYMODULE'),
  ('django.urls.exceptions',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\urls\\exceptions.py',
   'PYMODULE'),
  ('django.urls.resolvers',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\urls\\resolvers.py',
   'PYMODULE'),
  ('django.urls.utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\urls\\utils.py',
   'PYMODULE'),
  ('django.utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\__init__.py',
   'PYMODULE'),
  ('django.utils._os',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\_os.py',
   'PYMODULE'),
  ('django.utils.archive',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\archive.py',
   'PYMODULE'),
  ('django.utils.asyncio',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\asyncio.py',
   'PYMODULE'),
  ('django.utils.autoreload',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\autoreload.py',
   'PYMODULE'),
  ('django.utils.cache',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\cache.py',
   'PYMODULE'),
  ('django.utils.choices',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\choices.py',
   'PYMODULE'),
  ('django.utils.connection',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\connection.py',
   'PYMODULE'),
  ('django.utils.crypto',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\crypto.py',
   'PYMODULE'),
  ('django.utils.datastructures',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\datastructures.py',
   'PYMODULE'),
  ('django.utils.dateformat',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\dateformat.py',
   'PYMODULE'),
  ('django.utils.dateparse',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\dateparse.py',
   'PYMODULE'),
  ('django.utils.dates',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\dates.py',
   'PYMODULE'),
  ('django.utils.deconstruct',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\deconstruct.py',
   'PYMODULE'),
  ('django.utils.decorators',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\decorators.py',
   'PYMODULE'),
  ('django.utils.deprecation',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\deprecation.py',
   'PYMODULE'),
  ('django.utils.duration',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\duration.py',
   'PYMODULE'),
  ('django.utils.encoding',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\encoding.py',
   'PYMODULE'),
  ('django.utils.feedgenerator',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\feedgenerator.py',
   'PYMODULE'),
  ('django.utils.formats',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\formats.py',
   'PYMODULE'),
  ('django.utils.functional',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\functional.py',
   'PYMODULE'),
  ('django.utils.hashable',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\hashable.py',
   'PYMODULE'),
  ('django.utils.html',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\html.py',
   'PYMODULE'),
  ('django.utils.http',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\http.py',
   'PYMODULE'),
  ('django.utils.inspect',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\inspect.py',
   'PYMODULE'),
  ('django.utils.ipv6',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\ipv6.py',
   'PYMODULE'),
  ('django.utils.itercompat',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\itercompat.py',
   'PYMODULE'),
  ('django.utils.jslex',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\jslex.py',
   'PYMODULE'),
  ('django.utils.log',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\log.py',
   'PYMODULE'),
  ('django.utils.lorem_ipsum',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\lorem_ipsum.py',
   'PYMODULE'),
  ('django.utils.module_loading',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\module_loading.py',
   'PYMODULE'),
  ('django.utils.numberformat',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\numberformat.py',
   'PYMODULE'),
  ('django.utils.regex_helper',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\regex_helper.py',
   'PYMODULE'),
  ('django.utils.safestring',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\safestring.py',
   'PYMODULE'),
  ('django.utils.termcolors',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\termcolors.py',
   'PYMODULE'),
  ('django.utils.text',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\text.py',
   'PYMODULE'),
  ('django.utils.timesince',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\timesince.py',
   'PYMODULE'),
  ('django.utils.timezone',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\timezone.py',
   'PYMODULE'),
  ('django.utils.translation',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\translation\\__init__.py',
   'PYMODULE'),
  ('django.utils.translation.reloader',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\translation\\reloader.py',
   'PYMODULE'),
  ('django.utils.translation.template',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\translation\\template.py',
   'PYMODULE'),
  ('django.utils.translation.trans_null',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\translation\\trans_null.py',
   'PYMODULE'),
  ('django.utils.translation.trans_real',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\translation\\trans_real.py',
   'PYMODULE'),
  ('django.utils.tree',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\tree.py',
   'PYMODULE'),
  ('django.utils.version',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\version.py',
   'PYMODULE'),
  ('django.utils.xmlutils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\utils\\xmlutils.py',
   'PYMODULE'),
  ('django.views',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\views\\__init__.py',
   'PYMODULE'),
  ('django.views.csrf',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\views\\csrf.py',
   'PYMODULE'),
  ('django.views.debug',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\views\\debug.py',
   'PYMODULE'),
  ('django.views.decorators',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\views\\decorators\\__init__.py',
   'PYMODULE'),
  ('django.views.decorators.cache',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\views\\decorators\\cache.py',
   'PYMODULE'),
  ('django.views.decorators.clickjacking',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\views\\decorators\\clickjacking.py',
   'PYMODULE'),
  ('django.views.decorators.common',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\views\\decorators\\common.py',
   'PYMODULE'),
  ('django.views.decorators.csrf',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\views\\decorators\\csrf.py',
   'PYMODULE'),
  ('django.views.decorators.debug',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\views\\decorators\\debug.py',
   'PYMODULE'),
  ('django.views.decorators.gzip',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\views\\decorators\\gzip.py',
   'PYMODULE'),
  ('django.views.decorators.http',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\views\\decorators\\http.py',
   'PYMODULE'),
  ('django.views.decorators.vary',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\views\\decorators\\vary.py',
   'PYMODULE'),
  ('django.views.defaults',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\views\\defaults.py',
   'PYMODULE'),
  ('django.views.generic',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\views\\generic\\__init__.py',
   'PYMODULE'),
  ('django.views.generic.base',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\views\\generic\\base.py',
   'PYMODULE'),
  ('django.views.generic.dates',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\views\\generic\\dates.py',
   'PYMODULE'),
  ('django.views.generic.detail',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\views\\generic\\detail.py',
   'PYMODULE'),
  ('django.views.generic.edit',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\views\\generic\\edit.py',
   'PYMODULE'),
  ('django.views.generic.list',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\views\\generic\\list.py',
   'PYMODULE'),
  ('django.views.i18n',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\views\\i18n.py',
   'PYMODULE'),
  ('django.views.static',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\django\\views\\static.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\__init__.py',
   'PYMODULE'),
  ('email.mime.audio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\audio.py',
   'PYMODULE'),
  ('email.mime.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\base.py',
   'PYMODULE'),
  ('email.mime.image',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\image.py',
   'PYMODULE'),
  ('email.mime.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\message.py',
   'PYMODULE'),
  ('email.mime.multipart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\multipart.py',
   'PYMODULE'),
  ('email.mime.nonmultipart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\nonmultipart.py',
   'PYMODULE'),
  ('email.mime.text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\text.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\glob.py',
   'PYMODULE'),
  ('graphlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\graphlib.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('html.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\html\\parser.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\server.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('logging.config',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\logging\\config.py',
   'PYMODULE'),
  ('logging.handlers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\lzma.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\numbers.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\opcode.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\__init__.py',
   'PYMODULE'),
  ('pathlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\_abc.py',
   'PYMODULE'),
  ('pathlib._local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\_local.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pdb.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pickle.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\platform.py',
   'PYMODULE'),
  ('pos',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\pos\\__init__.py',
   'PYMODULE'),
  ('pos.admin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\pos\\admin.py',
   'PYMODULE'),
  ('pos.apps',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\pos\\apps.py',
   'PYMODULE'),
  ('pos.management',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\pos\\management\\__init__.py',
   'PYMODULE'),
  ('pos.management.commands',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\pos\\management\\commands\\__init__.py',
   'PYMODULE'),
  ('pos.management.commands.populate_data',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\pos\\management\\commands\\populate_data.py',
   'PYMODULE'),
  ('pos.management.commands.populate_sales',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\pos\\management\\commands\\populate_sales.py',
   'PYMODULE'),
  ('pos.management.commands.setup_demo',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\pos\\management\\commands\\setup_demo.py',
   'PYMODULE'),
  ('pos.migrations',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\pos\\migrations\\__init__.py',
   'PYMODULE'),
  ('pos.migrations.0001_initial',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\pos\\migrations\\0001_initial.py',
   'PYMODULE'),
  ('pos.migrations.0002_product_expiration_date',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\pos\\migrations\\0002_product_expiration_date.py',
   'PYMODULE'),
  ('pos.migrations.0003_alter_sale_id',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\pos\\migrations\\0003_alter_sale_id.py',
   'PYMODULE'),
  ('pos.migrations.0004_alter_sale_cashier',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\pos\\migrations\\0004_alter_sale_cashier.py',
   'PYMODULE'),
  ('pos.models',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\pos\\models.py',
   'PYMODULE'),
  ('pos.tests',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\pos\\tests.py',
   'PYMODULE'),
  ('pos.urls',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\pos\\urls.py',
   'PYMODULE'),
  ('pos.views',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\pos\\views.py',
   'PYMODULE'),
  ('pos_system',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\pos_system\\__init__.py',
   'PYMODULE'),
  ('pos_system.settings',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\pos_system\\settings.py',
   'PYMODULE'),
  ('pos_system.urls',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\pos_system\\urls.py',
   'PYMODULE'),
  ('pos_system.wsgi',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\pos_system\\wsgi.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pprint.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\random.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\runpy.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\selectors.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\signal.py',
   'PYMODULE'),
  ('smtplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\smtplib.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\socketserver.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('sqlparse',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\sqlparse\\__init__.py',
   'PYMODULE'),
  ('sqlparse.cli',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\sqlparse\\cli.py',
   'PYMODULE'),
  ('sqlparse.engine',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\sqlparse\\engine\\__init__.py',
   'PYMODULE'),
  ('sqlparse.engine.filter_stack',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\sqlparse\\engine\\filter_stack.py',
   'PYMODULE'),
  ('sqlparse.engine.grouping',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\sqlparse\\engine\\grouping.py',
   'PYMODULE'),
  ('sqlparse.engine.statement_splitter',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\sqlparse\\engine\\statement_splitter.py',
   'PYMODULE'),
  ('sqlparse.exceptions',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\sqlparse\\exceptions.py',
   'PYMODULE'),
  ('sqlparse.filters',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\sqlparse\\filters\\__init__.py',
   'PYMODULE'),
  ('sqlparse.filters.aligned_indent',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\sqlparse\\filters\\aligned_indent.py',
   'PYMODULE'),
  ('sqlparse.filters.others',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\sqlparse\\filters\\others.py',
   'PYMODULE'),
  ('sqlparse.filters.output',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\sqlparse\\filters\\output.py',
   'PYMODULE'),
  ('sqlparse.filters.reindent',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\sqlparse\\filters\\reindent.py',
   'PYMODULE'),
  ('sqlparse.filters.right_margin',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\sqlparse\\filters\\right_margin.py',
   'PYMODULE'),
  ('sqlparse.filters.tokens',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\sqlparse\\filters\\tokens.py',
   'PYMODULE'),
  ('sqlparse.formatter',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\sqlparse\\formatter.py',
   'PYMODULE'),
  ('sqlparse.keywords',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\sqlparse\\keywords.py',
   'PYMODULE'),
  ('sqlparse.lexer',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\sqlparse\\lexer.py',
   'PYMODULE'),
  ('sqlparse.sql',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\sqlparse\\sql.py',
   'PYMODULE'),
  ('sqlparse.tokens',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\sqlparse\\tokens.py',
   'PYMODULE'),
  ('sqlparse.utils',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\sqlparse\\utils.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ssl.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sysconfig\\__init__.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\threading.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tty.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('tzdata',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\tzdata\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Africa',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Africa\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Argentina',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Indiana',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Kentucky',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.North_Dakota',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Antarctica',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Arctic',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Arctic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Asia',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Asia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Atlantic',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Australia',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Australia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Brazil',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Canada',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Canada\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Chile',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Chile\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Etc',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Etc\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Europe',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Europe\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Indian',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Indian\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Mexico',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Pacific',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.US',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\tzdata\\zoneinfo\\US\\__init__.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\uuid.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('win32con',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('winerror',
   'C:\\Users\\<USER>\\djangoproject\\pos_system\\.venv\\Lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('wsgiref',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\wsgiref\\__init__.py',
   'PYMODULE'),
  ('wsgiref.handlers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\wsgiref\\handlers.py',
   'PYMODULE'),
  ('wsgiref.headers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\wsgiref\\headers.py',
   'PYMODULE'),
  ('wsgiref.simple_server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\wsgiref\\simple_server.py',
   'PYMODULE'),
  ('wsgiref.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\wsgiref\\util.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.dom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipimport.py',
   'PYMODULE'),
  ('zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('zoneinfo._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zoneinfo\\_common.py',
   'PYMODULE'),
  ('zoneinfo._tzpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zoneinfo\\_tzpath.py',
   'PYMODULE'),
  ('zoneinfo._zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zoneinfo\\_zoneinfo.py',
   'PYMODULE')])
