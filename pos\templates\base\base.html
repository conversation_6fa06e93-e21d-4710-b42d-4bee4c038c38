<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{% block title %}POS System{% endblock %}</title>

    <!-- Tailwind CSS -->
        <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Fonts -->
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Heroicons -->
        <script src="https://unpkg.com/@heroicons/v2/24/solid"></script>
        <script src="https://unpkg.com/heroicons@2.0.18/24/outline/index.js"></script>
        <link href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">


    <!-- Custom Styles -->
        <style>
            body {
                font-family: 'Inter', sans-serif;
            }
        </style>
        {% block extra_css %}{% endblock %}
    </head>
    <body class="bg-gray-50">
        <div class="min-h-screen flex">
        <!-- Sidebar -->
            <aside class="hidden md:flex md:flex-shrink-0">
                <div class="flex flex-col w-64 bg-gray-800">
                    <div class="flex items-center h-16 px-4">
                        <span class="text-white text-xl font-semibold">POS Dashboard</span>
                    </div>

                    <nav class="mt-5 flex-1 px-2 space-y-1">
                        <a href="{% url 'dashboard' %}" class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-white hover:bg-gray-700">
                            <svg class="mr-3 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                            </svg>
                            Dashboard
                        </a>

                        <a href="{% url 'products' %}" class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:bg-gray-700 hover:text-white">
                            <svg class="mr-3 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
                            </svg>
                            Products
                        </a>

                        <a href="{% url 'categories' %}" class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:bg-gray-700 hover:text-white">
                            <svg class="mr-3 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"/>
                            </svg>
                            Categories
                        </a>

                        <a href="{% url 'sales' %}" class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:bg-gray-700 hover:text-white">
                            <svg class="mr-3 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            Sales
                        </a>


                        <a href="{% url 'sales_report' %}" class="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:bg-gray-700 hover:text-white">
                            <svg class="mr-3 h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                            </svg>
                            Sales Report
                        </a>
                    </nav>

                </div>
            </aside>

        <!-- Main Content -->
            <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Navigation -->
                <header class="bg-white shadow-md">
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div class="flex justify-between h-16">
                            <div class="flex">
                                <div class="flex-shrink-0 flex items-center">
                                <!-- Mobile menu button -->
                                    <button type="button" class="md:hidden px-4 text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-300 rounded-md transition-colors duration-200">
                                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <div class="flex items-center">
                                {% if user.is_authenticated %}
                                    <div class="ml-3 relative">
                                        <div class="relative">
                                            <button type="button" onclick="toggleDropdown()" class="flex items-center space-x-3 bg-white rounded-lg hover:bg-gray-50 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200">
                                                <span class="text-gray-700 font-medium">{{ user.username }}</span>
                                                <div class="bg-indigo-600 p-1.5 rounded-full text-white hover:bg-indigo-700 transition-colors duration-200">
                                                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                    </svg>
                                                </div>
                                            </button>
                                            <div id="userDropdown" class="hidden absolute right-0 mt-2 w-48 rounded-lg shadow-lg bg-white ring-1 ring-black ring-opacity-5 transform transition-all duration-200">
                                                <div class="py-1" role="menu" aria-orientation="vertical">
                                                    <div class="px-4 py-2 text-sm text-gray-500 border-b">
                                                        <div class="flex items-center">
                                                            <i class="fas fa-user-shield text-indigo-600 mr-2"></i>
                                                            Owner Account
                                                        </div>
                                                    </div>
                                                    <a href="{% url 'dashboard' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-colors duration-150 flex items-center" role="menuitem">
                                                        <i class="fas fa-tachometer-alt mr-2"></i>
                                                        Dashboard
                                                    </a>
                                                    <a href="{% url 'landing' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-colors duration-150 flex items-center" role="menuitem">
                                                        <i class="fas fa-home mr-2"></i>
                                                        Home
                                                    </a>
                                                    <div class="border-t">
                                                        <a href="{% url 'logout' %}" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 hover:text-red-700 transition-colors duration-150 flex items-center" role="menuitem">
                                                            <i class="fas fa-sign-out-alt mr-2"></i>
                                                            Logout
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {% else %}
                                    <a href="{% url 'login' %}" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors duration-200 flex items-center font-medium">
                                        <i class="fas fa-sign-in-alt mr-2"></i>
                                        Login
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <script>
                        function toggleDropdown() {
                            const dropdown = document.getElementById('userDropdown');
                            dropdown.classList.toggle('hidden');
                            if (!dropdown.classList.contains('hidden')) {
                                dropdown.classList.add('opacity-100', 'scale-100');
                            } else {
                                dropdown.classList.remove('opacity-100', 'scale-100');
                            }
                        }

                        window.onclick = function(event) {
                            if (!event.target.closest('.relative')) {
                                var dropdown = document.getElementById('userDropdown');
                                if (!dropdown.classList.contains('hidden')) {
                                    dropdown.classList.add('hidden');
                                    dropdown.classList.remove('opacity-100', 'scale-100');
                                }
                            }
                        }
                    </script>
                </header>            <!-- Main Content Area -->
                <main class="flex-1 overflow-y-auto bg-gray-50">
                    <div class="py-6">
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            {% block content %}{% endblock %}
                        </div>
                    </div>
                </main>
            </div>
        </div>

        {% block extra_js %}{% endblock %}
    </body>
</html>


