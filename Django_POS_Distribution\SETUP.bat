@echo off
echo Django POS System - Complete Setup
echo =================================
echo.

REM Step 1: Install dependencies
echo Step 1: Installing dependencies...
call install_dependencies.bat
if errorlevel 1 (
    echo Setup failed at dependency installation
    pause
    exit /b 1
)

echo.
echo Step 2: Setting up database...
python manage.py migrate >nul 2>&1
if errorlevel 1 (
    echo Database setup had warnings (this is usually OK)
)

echo.
echo Step 3: Collecting static files...
python manage.py collectstatic --noinput >nul 2>&1

echo.
echo Step 4: Creating desktop shortcut...
set CURRENT_DIR=%~dp0
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\Django POS System.lnk'); $Shortcut.TargetPath = '%CURRENT_DIR%Django_POS_System.bat'; $Shortcut.WorkingDirectory = '%CURRENT_DIR%'; $Shortcut.Description = 'Django POS System'; $Shortcut.Save()" 2>nul

echo.
echo Setup completed successfully!
echo.
echo Django POS System is ready to use!
echo.
echo You can start it by:
echo 1. Double-clicking the desktop shortcut "Django POS System"
echo 2. Double-clicking "Django_POS_System.bat" in this folder
echo 3. Running "python pos_launcher.py" for GUI version
echo.
echo The system will:
echo - Start automatically
echo - Open in your web browser
echo - Be ready for use immediately
echo.
pause
