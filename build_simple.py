#!/usr/bin/env python3
"""
Simple Build Script - Creates a reliable executable
"""

import os
import sys
import subprocess
from pathlib import Path
import shutil

def build_simple_exe():
    """Build a simple, reliable executable"""
    project_dir = Path(__file__).resolve().parent
    
    print("🔨 Building Simple Django POS Executable...")
    print("=" * 50)
    
    # Remove old dist folder if it exists
    dist_dir = project_dir / 'dist_simple'
    if dist_dir.exists():
        try:
            shutil.rmtree(dist_dir)
            print("✅ Cleaned old build")
        except PermissionError:
            print("⚠️  Could not remove old build - files may be in use")
            print("Please close any running POS applications and try again")
            return False
    
    # Simple PyInstaller command
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',
        '--console',  # Console version is more reliable
        '--name=Django_POS_Simple',
        '--distpath=dist_simple',
        '--workpath=build_simple',
        '--specpath=.',
        # Include essential files
        '--add-data=manage.py;.',
        '--add-data=pos_system;pos_system',
        '--add-data=pos;pos',
        '--add-data=static;static',
        # Essential Django imports
        '--hidden-import=django',
        '--hidden-import=django.core.management',
        '--hidden-import=django.core.management.commands.runserver',
        'simple_pos_launcher.py'
    ]
    
    print("🚀 Running PyInstaller...")
    print("This may take a few minutes...")
    
    try:
        result = subprocess.run(cmd, cwd=project_dir, check=True, 
                              capture_output=True, text=True)
        print("✅ Build completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        print("Error output:", e.stderr)
        return False

def create_simple_installer():
    """Create a simple installer"""
    installer_content = '''@echo off
echo Django POS System - Simple Installer
echo ===================================
echo.

set INSTALL_DIR=%USERPROFILE%\\Django_POS_Simple

echo Installing to: %INSTALL_DIR%
echo.

if exist "%INSTALL_DIR%" (
    echo Removing old installation...
    rmdir /s /q "%INSTALL_DIR%" 2>nul
)

mkdir "%INSTALL_DIR%"

echo Copying files...
copy "Django_POS_Simple.exe" "%INSTALL_DIR%\\" >nul

echo Creating desktop shortcut...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\Django POS System.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\Django_POS_Simple.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Django POS System'; $Shortcut.Save()" 2>nul

echo.
echo ✅ Installation completed!
echo.
echo You can now:
echo 1. Double-click the desktop shortcut "Django POS System"
echo 2. Or run: %INSTALL_DIR%\\Django_POS_Simple.exe
echo.
echo The POS system will:
echo - Start automatically
echo - Open in your browser
echo - Show a console window (keep it open while using)
echo.
pause
'''
    
    dist_dir = Path('dist_simple')
    installer_file = dist_dir / 'install.bat'
    
    try:
        with open(installer_file, 'w') as f:
            f.write(installer_content)
        print(f"✅ Created installer: {installer_file}")
        return True
    except Exception as e:
        print(f"❌ Failed to create installer: {e}")
        return False

def create_readme():
    """Create a simple README"""
    readme_content = '''Django POS System - Simple Version
==================================

QUICK START:
-----------
1. Run "install.bat" as administrator
2. Double-click the desktop shortcut "Django POS System"
3. Wait for the browser to open automatically
4. Use the POS system normally

IMPORTANT:
---------
- Keep the console window open while using the POS system
- Close the console window to stop the POS system
- The system runs at: http://127.0.0.1:8000

FEATURES:
--------
- Complete Point of Sale system
- Product and inventory management
- Sales processing and receipts
- Category management
- Sales reporting

TROUBLESHOOTING:
---------------
- If browser doesn't open: Go to http://127.0.0.1:8000 manually
- If "port in use" error: Close any other POS applications
- If application won't start: Run as administrator

SYSTEM REQUIREMENTS:
-------------------
- Windows 10 or later
- 4GB RAM minimum
- Modern web browser

This is a simplified version that avoids complex GUI issues
and provides a reliable console-based experience.
'''
    
    dist_dir = Path('dist_simple')
    readme_file = dist_dir / 'README.txt'
    
    try:
        with open(readme_file, 'w') as f:
            f.write(readme_content)
        print(f"✅ Created README: {readme_file}")
        return True
    except Exception as e:
        print(f"❌ Failed to create README: {e}")
        return False

def main():
    """Main build process"""
    print("Django POS System - Simple Build")
    print("=" * 35)
    print()
    
    if build_simple_exe():
        print()
        print("Creating distribution files...")
        create_simple_installer()
        create_readme()
        
        print()
        print("🎊 SUCCESS! Simple executable created!")
        print("=" * 40)
        print()
        print("📦 Files created in 'dist_simple' folder:")
        print("  - Django_POS_Simple.exe")
        print("  - install.bat")
        print("  - README.txt")
        print()
        print("👥 For end users:")
        print("  1. Share the 'dist_simple' folder")
        print("  2. Users run 'install.bat' as administrator")
        print("  3. Users double-click desktop shortcut")
        print()
        print("✅ This version:")
        print("  - Avoids PyConfig issues")
        print("  - Shows console window (more reliable)")
        print("  - Works on all Windows versions")
        print("  - Provides clear status messages")
        
    else:
        print("❌ Build failed. Please check the errors above.")

if __name__ == "__main__":
    main()
