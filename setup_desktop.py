#!/usr/bin/env python3
"""
Desktop Setup Script for Django POS System
Automatically configures the POS system for desktop use.
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DesktopSetup:
    def __init__(self):
        self.project_dir = Path(__file__).resolve().parent
        self.requirements_file = self.project_dir / 'requirements_desktop.txt'
        
    def print_banner(self):
        """Print setup banner"""
        print("=" * 60)
        print("    Django POS System - Desktop Setup")
        print("=" * 60)
        print()
    
    def check_python_version(self):
        """Check if Python version is compatible"""
        logger.info("Checking Python version...")
        
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            logger.error(f"Python 3.8+ required, found {version.major}.{version.minor}")
            return False
        
        logger.info(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    
    def install_dependencies(self):
        """Install required dependencies"""
        logger.info("Installing desktop dependencies...")
        
        if not self.requirements_file.exists():
            logger.error(f"Requirements file not found: {self.requirements_file}")
            return False
        
        try:
            cmd = [sys.executable, '-m', 'pip', 'install', '-r', str(self.requirements_file)]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("✅ Dependencies installed successfully")
                return True
            else:
                logger.error(f"Failed to install dependencies: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error installing dependencies: {e}")
            return False
    
    def setup_database(self):
        """Setup database and run migrations"""
        logger.info("Setting up database...")
        
        try:
            # Run migrations
            cmd = [sys.executable, 'manage.py', 'migrate']
            result = subprocess.run(cmd, cwd=self.project_dir, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("✅ Database migrations completed")
            else:
                logger.warning(f"Migration warning: {result.stderr}")
            
            # Collect static files
            cmd = [sys.executable, 'manage.py', 'collectstatic', '--noinput']
            result = subprocess.run(cmd, cwd=self.project_dir, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("✅ Static files collected")
            else:
                logger.warning(f"Static files warning: {result.stderr}")
            
            return True
            
        except Exception as e:
            logger.error(f"Database setup error: {e}")
            return False
    
    def create_directories(self):
        """Create necessary directories"""
        logger.info("Creating directories...")
        
        directories = [
            'logs',
            'backups',
            'desktop_static',
            'desktop_media',
        ]
        
        for dir_name in directories:
            dir_path = self.project_dir / dir_name
            dir_path.mkdir(exist_ok=True)
            logger.info(f"✅ Created directory: {dir_name}")
        
        return True
    
    def test_desktop_launcher(self):
        """Test if desktop launcher works"""
        logger.info("Testing desktop launcher...")
        
        try:
            # Import the desktop launcher to check for import errors
            sys.path.insert(0, str(self.project_dir))
            from desktop_launcher import DjangoPOSDesktop
            
            app = DjangoPOSDesktop()
            if app.check_dependencies():
                logger.info("✅ Desktop launcher test passed")
                return True
            else:
                logger.error("Desktop launcher dependencies check failed")
                return False
                
        except Exception as e:
            logger.error(f"Desktop launcher test failed: {e}")
            return False
    
    def configure_auto_startup(self, enable=False):
        """Configure auto-startup if requested"""
        if not enable:
            return True
            
        logger.info("Configuring auto-startup...")
        
        try:
            from auto_startup import AutoStartupManager
            
            manager = AutoStartupManager()
            if manager.enable_auto_startup():
                logger.info("✅ Auto-startup enabled")
                return True
            else:
                logger.error("Failed to enable auto-startup")
                return False
                
        except Exception as e:
            logger.error(f"Auto-startup configuration error: {e}")
            return False
    
    def create_desktop_shortcut(self, create=False):
        """Create desktop shortcut if requested"""
        if not create:
            return True
            
        logger.info("Creating desktop shortcut...")
        
        try:
            from auto_startup import AutoStartupManager
            
            manager = AutoStartupManager()
            if manager.create_desktop_shortcut():
                logger.info("✅ Desktop shortcut created")
                return True
            else:
                logger.error("Failed to create desktop shortcut")
                return False
                
        except Exception as e:
            logger.error(f"Desktop shortcut creation error: {e}")
            return False
    
    def run_setup(self, auto_startup=False, desktop_shortcut=False):
        """Run the complete setup process"""
        self.print_banner()
        
        steps = [
            ("Checking Python version", self.check_python_version),
            ("Installing dependencies", self.install_dependencies),
            ("Creating directories", self.create_directories),
            ("Setting up database", self.setup_database),
            ("Testing desktop launcher", self.test_desktop_launcher),
        ]
        
        if auto_startup:
            steps.append(("Configuring auto-startup", lambda: self.configure_auto_startup(True)))
        
        if desktop_shortcut:
            steps.append(("Creating desktop shortcut", lambda: self.create_desktop_shortcut(True)))
        
        # Run all steps
        for step_name, step_func in steps:
            logger.info(f"Step: {step_name}")
            if not step_func():
                logger.error(f"❌ Setup failed at: {step_name}")
                return False
            print()
        
        # Success message
        print("=" * 60)
        print("🎉 Desktop setup completed successfully!")
        print("=" * 60)
        print()
        print("Next steps:")
        print("1. Run the desktop application:")
        print("   python desktop_launcher.py")
        print()
        print("2. Or use the batch file:")
        print("   start_pos_desktop.bat")
        print()
        print("3. For system tray mode:")
        print("   python system_tray.py")
        print()
        
        if auto_startup:
            print("✅ Auto-startup is enabled - POS will start with Windows")
        
        if desktop_shortcut:
            print("✅ Desktop shortcut created")
        
        print("📖 See README_DESKTOP.md for detailed usage instructions")
        print("=" * 60)
        
        return True

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Setup Django POS Desktop Application")
    parser.add_argument('--auto-startup', action='store_true', 
                       help='Enable auto-startup with Windows')
    parser.add_argument('--desktop-shortcut', action='store_true',
                       help='Create desktop shortcut')
    parser.add_argument('--full', action='store_true',
                       help='Full setup with auto-startup and desktop shortcut')
    
    args = parser.parse_args()
    
    # Full setup includes both auto-startup and desktop shortcut
    if args.full:
        args.auto_startup = True
        args.desktop_shortcut = True
    
    # Run setup
    setup = DesktopSetup()
    success = setup.run_setup(
        auto_startup=args.auto_startup,
        desktop_shortcut=args.desktop_shortcut
    )
    
    if not success:
        print("\n❌ Setup failed! Check the logs above for details.")
        input("Press Enter to exit...")
        sys.exit(1)
    else:
        print("\n🚀 Setup completed! Your POS system is ready for desktop use.")
        input("Press Enter to exit...")

if __name__ == '__main__':
    main()
