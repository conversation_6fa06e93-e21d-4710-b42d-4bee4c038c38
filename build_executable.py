#!/usr/bin/env python3
"""
Build Executable for Django POS Desktop Application
Creates a standalone executable using PyInstaller.
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ExecutableBuilder:
    def __init__(self):
        self.project_dir = Path(__file__).resolve().parent
        self.build_dir = self.project_dir / 'build'
        self.dist_dir = self.project_dir / 'dist'
        self.spec_file = self.project_dir / 'pos_desktop.spec'
        
    def clean_build_directories(self):
        """Clean previous build directories"""
        logger.info("Cleaning previous build directories...")
        
        for directory in [self.build_dir, self.dist_dir]:
            if directory.exists():
                shutil.rmtree(directory)
                logger.info(f"Removed {directory}")
        
        if self.spec_file.exists():
            self.spec_file.unlink()
            logger.info(f"Removed {self.spec_file}")
    
    def check_dependencies(self):
        """Check if required dependencies are installed"""
        logger.info("Checking dependencies...")
        
        required_packages = ['pyinstaller', 'pystray', 'pillow', 'django']
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
                logger.info(f"✅ {package} is installed")
            except ImportError:
                missing_packages.append(package)
                logger.error(f"❌ {package} is missing")
        
        if missing_packages:
            logger.error(f"Missing packages: {', '.join(missing_packages)}")
            logger.info("Install missing packages with: pip install " + " ".join(missing_packages))
            return False
        
        return True
    
    def create_pyinstaller_spec(self):
        """Create PyInstaller spec file"""
        logger.info("Creating PyInstaller spec file...")
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# Project directory
project_dir = Path(r"{self.project_dir}")

# Data files to include
datas = [
    (str(project_dir / "pos" / "templates"), "pos/templates"),
    (str(project_dir / "pos" / "static"), "pos/static"),
    (str(project_dir / "static"), "static"),
    (str(project_dir / "pos" / "management"), "pos/management"),
    (str(project_dir / "pos_system"), "pos_system"),
    (str(project_dir / "manage.py"), "."),
    (str(project_dir / "desktop_app_settings.py"), "."),
]

# Hidden imports
hiddenimports = [
    'django',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.humanize',
    'pos',
    'pos.models',
    'pos.views',
    'pos.urls',
    'pos.admin',
    'pos.apps',
    'pos_system.settings',
    'pos_system.urls',
    'pos_system.wsgi',
    'pystray',
    'PIL',
    'PIL.Image',
    'PIL.ImageDraw',
    'win32com.client',
    'winreg',
    'psutil',
]

# Analysis
a = Analysis(
    [str(project_dir / "system_tray.py")],
    pathex=[str(project_dir)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# PYZ
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# EXE
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='Django_POS_System',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Set to False for windowed app
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # Add icon file path here if you have one
)
'''
        
        try:
            with open(self.spec_file, 'w') as f:
                f.write(spec_content)
            logger.info(f"Created spec file: {self.spec_file}")
            return True
        except Exception as e:
            logger.error(f"Failed to create spec file: {e}")
            return False
    
    def run_pyinstaller(self):
        """Run PyInstaller to build the executable"""
        logger.info("Running PyInstaller...")
        
        try:
            cmd = [
                sys.executable, '-m', 'PyInstaller',
                '--clean',
                '--noconfirm',
                str(self.spec_file)
            ]
            
            logger.info(f"Running command: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                cwd=self.project_dir,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                logger.info("PyInstaller completed successfully!")
                logger.info("Build output:")
                logger.info(result.stdout)
                return True
            else:
                logger.error("PyInstaller failed!")
                logger.error("Error output:")
                logger.error(result.stderr)
                return False
                
        except Exception as e:
            logger.error(f"Failed to run PyInstaller: {e}")
            return False
    
    def copy_additional_files(self):
        """Copy additional files to the distribution directory"""
        logger.info("Copying additional files...")
        
        exe_dir = self.dist_dir / "Django_POS_System"
        if not exe_dir.exists():
            logger.error("Executable directory not found!")
            return False
        
        # Files to copy
        additional_files = [
            'db.sqlite3',
            'requirements_desktop.txt',
            'README_DESKTOP.md',
        ]
        
        for file_name in additional_files:
            src_file = self.project_dir / file_name
            if src_file.exists():
                dst_file = exe_dir / file_name
                try:
                    shutil.copy2(src_file, dst_file)
                    logger.info(f"Copied {file_name}")
                except Exception as e:
                    logger.warning(f"Failed to copy {file_name}: {e}")
        
        # Create directories
        directories = ['logs', 'backups', 'desktop_static', 'desktop_media']
        for dir_name in directories:
            dir_path = exe_dir / dir_name
            dir_path.mkdir(exist_ok=True)
            logger.info(f"Created directory: {dir_name}")
        
        return True
    
    def create_installer_script(self):
        """Create a simple installer script"""
        logger.info("Creating installer script...")
        
        installer_content = '''@echo off
echo Django POS System Installer
echo ========================

set INSTALL_DIR=%USERPROFILE%\\Django_POS_System

echo Installing to: %INSTALL_DIR%

if exist "%INSTALL_DIR%" (
    echo Removing existing installation...
    rmdir /s /q "%INSTALL_DIR%"
)

echo Copying files...
xcopy /e /i /h /y "Django_POS_System" "%INSTALL_DIR%"

echo Creating desktop shortcut...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\Django POS System.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\Django_POS_System.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Save()"

echo Installation completed!
echo You can now run Django POS System from your desktop.
pause
'''
        
        installer_file = self.dist_dir / "install.bat"
        try:
            with open(installer_file, 'w') as f:
                f.write(installer_content)
            logger.info(f"Created installer script: {installer_file}")
            return True
        except Exception as e:
            logger.error(f"Failed to create installer script: {e}")
            return False
    
    def build(self):
        """Main build process"""
        logger.info("=" * 50)
        logger.info("Building Django POS Desktop Application")
        logger.info("=" * 50)
        
        try:
            # Check dependencies
            if not self.check_dependencies():
                return False
            
            # Clean previous builds
            self.clean_build_directories()
            
            # Create spec file
            if not self.create_pyinstaller_spec():
                return False
            
            # Run PyInstaller
            if not self.run_pyinstaller():
                return False
            
            # Copy additional files
            if not self.copy_additional_files():
                return False
            
            # Create installer script
            self.create_installer_script()
            
            logger.info("=" * 50)
            logger.info("Build completed successfully!")
            logger.info(f"Executable location: {self.dist_dir / 'Django_POS_System'}")
            logger.info("=" * 50)
            
            return True
            
        except Exception as e:
            logger.error(f"Build failed: {e}")
            return False

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Build Django POS Desktop Application")
    parser.add_argument('--clean', action='store_true', help='Clean build directories only')
    
    args = parser.parse_args()
    
    builder = ExecutableBuilder()
    
    if args.clean:
        builder.clean_build_directories()
        print("Build directories cleaned.")
    else:
        success = builder.build()
        if success:
            print("\\n✅ Build completed successfully!")
            print(f"📁 Executable location: {builder.dist_dir / 'Django_POS_System'}")
            print("🚀 Run the installer or copy the folder to your desired location.")
        else:
            print("\\n❌ Build failed! Check the logs for details.")
            sys.exit(1)

if __name__ == '__main__':
    main()
