# BIR Compliance Guide for Django POS System

## Overview

This guide helps you configure the POS system to comply with Bureau of Internal Revenue (BIR) requirements for sales receipts in the Philippines.

## BIR Requirements for Sales Receipts

### Mandatory Information on Receipts:
1. **Business Name and Address**
2. **TIN (Tax Identification Number)**
3. **ATP (Authority to Print) Number**
4. **ATP Date Issued**
5. **Receipt Series Range**
6. **Validity Period Notice**
7. **Input Tax Disclaimer**

## Quick Setup

### Option 1: Interactive Setup
```bash
python manage.py setup_bir --interactive
```

### Option 2: Command Line Setup
```bash
python manage.py setup_bir \
  --tin "***********-000" \
  --atp-number "ATP123456789" \
  --atp-date "2024-01-01" \
  --business-name "Your Business Name" \
  --business-address "Your Business Address"
```

## Manual Configuration

### Step 1: Update BIR Information

Edit the following placeholders in your receipt templates:

**In Receipt Templates:**
- `[Your TIN Number]` → Your actual TIN (e.g., "***********-000")
- `[ATP Number]` → Your ATP number (e.g., "ATP123456789")
- `[ATP Date]` → ATP issue date (e.g., "2024-01-01")

**Files to Update:**
- `pos/templates/sales/create.html`
- `pos/static/js/receipt.js`

### Step 2: Configure BIR Settings

Edit `pos/bir_config.py`:

```python
BIR_SETTINGS = {
    'business_name': 'Your Actual Business Name',
    'business_address': 'Your Complete Business Address',
    'tin_number': '***********-000',
    'atp_number': 'ATP123456789',
    'atp_date_issued': '2024-01-01',
    'atp_valid_until': '2029-01-01',
    'receipt_series_from': '0000001',
    'receipt_series_to': '9999999',
}
```

## BIR Compliance Text Added

The following compliance text has been added to all receipts:

### Main Receipt (Thermal Printer Format):
```
BIR COMPLIANCE NOTICE
This receipt shall be valid for
five (5) years from the date of
the ATP issuance.
THIS DOCUMENT IS NOT VALID
FOR CLAIM OF INPUT TAX
TIN: [Your TIN Number]
Permit No: [ATP Number]
Date Issued: [ATP Date]
```

### Reprint Receipt (Standard Format):
```
BIR COMPLIANCE NOTICE
This receipt shall be valid for five (5) years from the date of the ATP issuance.
THIS DOCUMENT IS NOT VALID FOR CLAIM OF INPUT TAX
TIN: [Your TIN Number]
Permit No: [ATP Number]
Date Issued: [ATP Date]
```

## Required BIR Documents

Before using the POS system for business:

1. **Certificate of Registration (COR)**
   - Register your business with BIR
   - Obtain your TIN

2. **Authority to Print (ATP)**
   - Apply for ATP at BIR
   - Specify receipt series range
   - Valid for 5 years

3. **Books of Accounts Registration**
   - Register your accounting books
   - Include computerized books if applicable

## Legal Requirements

### Receipt Validity:
- Receipts are valid for 5 years from ATP issuance
- Must display validity period notice
- Must include input tax disclaimer

### Record Keeping:
- Keep all receipts for 5 years minimum
- Maintain proper books of accounts
- Submit required BIR reports (monthly/quarterly)

### Penalties to Avoid:
- ₱1,000 - ₱50,000 for using receipts without ATP
- ₱500 - ₱25,000 for incorrect TIN display
- ₱1,000 - ₱50,000 for missing compliance text

## Testing Your Setup

### 1. Print Test Receipt:
1. Process a test sale
2. Print the receipt
3. Verify all BIR information is correct
4. Check compliance text is present

### 2. Verify Information:
- [ ] Business name and address correct
- [ ] TIN number displayed correctly
- [ ] ATP number and date shown
- [ ] Validity notice present
- [ ] Input tax disclaimer included

## Troubleshooting

### Common Issues:

**Problem:** Placeholder text still showing
**Solution:** Update the template files with actual BIR information

**Problem:** Receipt format looks wrong
**Solution:** Check CSS styling and printer settings

**Problem:** Missing compliance text
**Solution:** Verify template updates were applied correctly

## Professional Consultation

**Important:** This guide provides general information about BIR compliance. For specific legal advice and to ensure full compliance with current BIR regulations, consult with:

- BIR-accredited tax professional
- Certified Public Accountant (CPA)
- Tax lawyer specializing in Philippine tax law

## Support

For technical support with the POS system:
1. Check the receipt templates are updated
2. Verify BIR configuration is correct
3. Test print receipts before going live
4. Keep backup of your BIR documents

## Updates and Changes

BIR regulations may change. Regularly:
1. Check BIR website for updates
2. Consult with tax professional
3. Update receipt templates as needed
4. Renew ATP before expiration

---

**Disclaimer:** This software provides tools to help with BIR compliance but does not guarantee legal compliance. Users are responsible for ensuring their business operations comply with all applicable laws and regulations.
