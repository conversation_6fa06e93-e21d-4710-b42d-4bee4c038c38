@echo off
echo Django POS System - Simple Installer
echo ===================================
echo.

set INSTALL_DIR=%USERPROFILE%\Django_POS_Simple

echo Installing to: %INSTALL_DIR%
echo.

if exist "%INSTALL_DIR%" (
    echo Removing old installation...
    rmdir /s /q "%INSTALL_DIR%" 2>nul
)

mkdir "%INSTALL_DIR%"

echo Copying files...
copy "Django_POS_Simple.exe" "%INSTALL_DIR%\" >nul

echo Creating desktop shortcut...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Django POS System.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\Django_POS_Simple.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Django POS System'; $Shortcut.Save()" 2>nul

echo.
echo Installation completed!
echo.
echo You can now:
echo 1. Double-click the desktop shortcut "Django POS System"
echo 2. Or run: %INSTALL_DIR%\Django_POS_Simple.exe
echo.
echo The POS system will:
echo - Start automatically
echo - Open in your browser
echo - Show a console window (keep it open while using)
echo.
pause