Django POS System - Simple Version
==================================

QUICK START:
-----------
1. Run "install.bat" as administrator
2. Double-click the desktop shortcut "Django POS System"
3. Wait for the browser to open automatically
4. Use the POS system normally

IMPORTANT:
---------
- Keep the console window open while using the POS system
- Close the console window to stop the POS system
- The system runs at: http://127.0.0.1:8000

FEATURES:
--------
- Complete Point of Sale system
- Product and inventory management
- Sales processing and receipts
- Category management
- Sales reporting

TROUBLESHOOTING:
---------------
- If browser doesn't open: Go to http://127.0.0.1:8000 manually
- If "port in use" error: Close any other POS applications
- If application won't start: Run as administrator

SYSTEM REQUIREMENTS:
-------------------
- Windows 10 or later
- 4GB RAM minimum
- Modern web browser

This is a simplified version that avoids complex GUI issues
and provides a reliable console-based experience.
