@echo off
echo Django POS System Installer
echo ==========================
echo.

set INSTALL_DIR=%USERPROFILE%\Django_POS_System

echo Installing Django POS System to: %INSTALL_DIR%
echo.

if exist "%INSTALL_DIR%" (
    echo Removing existing installation...
    rmdir /s /q "%INSTALL_DIR%"
)

echo Creating installation directory...
mkdir "%INSTALL_DIR%"

echo Copying executable...
copy "Django_POS_System.exe" "%INSTALL_DIR%\"

echo Creating desktop shortcut...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Django POS System.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\Django_POS_System.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Django POS System - Point of Sale Application'; $Shortcut.Save()"

echo.
echo Installation completed successfully!
echo.
echo You can now:
echo 1. Run Django POS System from your desktop shortcut
echo 2. Or run it from: %INSTALL_DIR%\Django_POS_System.exe
echo.
pause