#!/usr/bin/env python3
"""
Build Simple Executable for Django POS System
Creates a standalone .exe file that users can double-click to run.
"""

import os
import sys
import subprocess
from pathlib import Path
import shutil

def build_executable():
    """Build the executable using PyInstaller"""
    project_dir = Path(__file__).resolve().parent
    
    print("🔨 Building Django POS System Executable...")
    print("=" * 50)
    
    # Clean previous builds
    build_dir = project_dir / 'build'
    dist_dir = project_dir / 'dist'
    
    if build_dir.exists():
        shutil.rmtree(build_dir)
        print("✅ Cleaned build directory")
    
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
        print("✅ Cleaned dist directory")
    
    # PyInstaller command
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',                    # Single executable file
        '--windowed',                   # No console window
        '--name=Django_POS_System',     # Executable name
        '--icon=NONE',                  # No icon (you can add one later)
        '--add-data=pos;pos',           # Include pos app
        '--add-data=pos_system;pos_system',  # Include settings
        '--add-data=static;static',     # Include static files
        '--add-data=manage.py;.',       # Include manage.py
        '--add-data=desktop_app_settings.py;.',  # Include desktop settings
        '--hidden-import=django',
        '--hidden-import=django.contrib.admin',
        '--hidden-import=django.contrib.auth',
        '--hidden-import=django.contrib.contenttypes',
        '--hidden-import=django.contrib.sessions',
        '--hidden-import=django.contrib.messages',
        '--hidden-import=django.contrib.staticfiles',
        '--hidden-import=django.contrib.humanize',
        '--hidden-import=pos',
        '--hidden-import=pos.models',
        '--hidden-import=pos.views',
        '--hidden-import=pos.urls',
        '--hidden-import=tkinter',
        'pos_launcher.py'
    ]
    
    print("🚀 Running PyInstaller...")
    print(f"Command: {' '.join(cmd)}")
    print()
    
    try:
        result = subprocess.run(cmd, cwd=project_dir, check=True)
        
        print()
        print("✅ Build completed successfully!")
        print(f"📁 Executable location: {dist_dir / 'Django_POS_System.exe'}")
        print()
        print("🎉 You can now distribute the .exe file!")
        print("Users just need to double-click it to run the POS system.")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def create_installer():
    """Create a simple installer script"""
    project_dir = Path(__file__).resolve().parent
    dist_dir = project_dir / 'dist'
    
    installer_content = f'''@echo off
echo Django POS System Installer
echo ==========================
echo.

set INSTALL_DIR=%USERPROFILE%\\Django_POS_System

echo Installing Django POS System to: %INSTALL_DIR%
echo.

if exist "%INSTALL_DIR%" (
    echo Removing existing installation...
    rmdir /s /q "%INSTALL_DIR%"
)

echo Creating installation directory...
mkdir "%INSTALL_DIR%"

echo Copying executable...
copy "Django_POS_System.exe" "%INSTALL_DIR%\\"

echo Creating desktop shortcut...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\Django POS System.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\Django_POS_System.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Django POS System - Point of Sale Application'; $Shortcut.Save()"

echo.
echo ✅ Installation completed successfully!
echo.
echo You can now:
echo 1. Run Django POS System from your desktop shortcut
echo 2. Or run it from: %INSTALL_DIR%\\Django_POS_System.exe
echo.
pause
'''
    
    installer_file = dist_dir / "install.bat"
    try:
        with open(installer_file, 'w') as f:
            f.write(installer_content)
        print(f"✅ Created installer: {installer_file}")
        return True
    except Exception as e:
        print(f"❌ Failed to create installer: {e}")
        return False

if __name__ == "__main__":
    print("Django POS System - Executable Builder")
    print("=" * 40)
    print()
    
    # Check if PyInstaller is installed
    try:
        import PyInstaller
        print("✅ PyInstaller is available")
    except ImportError:
        print("❌ PyInstaller not found. Installing...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
    
    # Build executable
    if build_executable():
        create_installer()
        print()
        print("🎊 SUCCESS! Your Django POS System is now ready for distribution!")
        print()
        print("📦 Distribution files:")
        print("  - Django_POS_System.exe (main executable)")
        print("  - install.bat (installer script)")
        print()
        print("👥 For end users:")
        print("  1. Give them the 'dist' folder")
        print("  2. They run 'install.bat' as administrator")
        print("  3. They get a desktop shortcut to start the POS system")
    else:
        print("❌ Build failed. Please check the errors above.")
