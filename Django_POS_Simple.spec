# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['simple_pos_launcher.py'],
    pathex=[],
    binaries=[],
    datas=[('manage.py', '.'), ('pos_system', 'pos_system'), ('pos', 'pos'), ('static', 'static')],
    hiddenimports=['django', 'django.core.management', 'django.core.management.commands.runserver'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='Django_POS_Simple',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
