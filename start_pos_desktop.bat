@echo off
title Django POS Desktop Application

echo ========================================
echo    Django POS Desktop Application
echo ========================================
echo.

REM Change to the script directory
cd /d "%~dp0"

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python and try again.
    pause
    exit /b 1
)

echo Python found. Starting POS system...
echo.

REM Try to install dependencies if they're missing
echo Checking dependencies...
python -c "import pystray, PIL" >nul 2>&1
if errorlevel 1 (
    echo Installing desktop dependencies...
    python -m pip install -r requirements_desktop.txt
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)

echo Dependencies OK. Launching desktop application...
echo.

REM Start the desktop application
python desktop_launcher.py

echo.
echo POS system has stopped.
pause
