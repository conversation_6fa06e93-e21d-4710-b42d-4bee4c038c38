# 🖥️ Django POS System - Desktop Application

Transform your Django POS system into a standalone desktop application that runs automatically without manual server management!

## 🚀 Quick Start

### Option 1: Simple Desktop Launcher (Recommended for Development)

1. **Install Desktop Dependencies**
   ```bash
   pip install -r requirements_desktop.txt
   ```

2. **Run Desktop Application**
   ```bash
   python desktop_launcher.py
   ```

3. **System Tray Mode**
   ```bash
   python system_tray.py
   ```

### Option 2: Auto-Startup Configuration

1. **Enable Auto-Startup**
   ```bash
   python auto_startup.py --enable
   ```

2. **Create Desktop Shortcut**
   ```bash
   python auto_startup.py --desktop-shortcut
   ```

3. **Check Status**
   ```bash
   python auto_startup.py --status
   ```

### Option 3: Standalone Executable (For Distribution)

1. **Build Executable**
   ```bash
   python build_executable.py
   ```

2. **Install Built Application**
   - Navigate to `dist/` folder
   - Run `install.bat` as administrator
   - Or manually copy `Django_POS_System` folder to desired location

## 📋 Features

### ✅ Desktop Application Features
- **🔄 Automatic Server Management** - No manual `python manage.py runserver`
- **🌐 Auto Browser Opening** - Automatically opens POS system in browser
- **📍 System Tray Integration** - Minimize to system tray with right-click menu
- **🚀 Auto-Startup** - Start automatically when Windows boots
- **🔧 Port Management** - Automatically finds available ports
- **📊 Status Monitoring** - Real-time server status and notifications
- **📝 Logging** - Comprehensive logging for troubleshooting
- **🖱️ Desktop Shortcuts** - Easy access from desktop
- **📦 Standalone Executable** - Single-file distribution

### 🎯 POS System Features (Unchanged)
- Product management with categories and barcodes
- Sales processing with receipt generation
- Inventory tracking and low stock alerts
- Dashboard with sales analytics
- User authentication and management
- QR code payment integration

## 🛠️ Installation Methods

### Method 1: Development Setup

```bash
# 1. Clone/navigate to your POS project
cd your_pos_project

# 2. Install desktop dependencies
pip install -r requirements_desktop.txt

# 3. Run desktop application
python desktop_launcher.py
```

### Method 2: Production Setup with Auto-Startup

```bash
# 1. Install dependencies
pip install -r requirements_desktop.txt

# 2. Enable auto-startup
python auto_startup.py --enable

# 3. Create desktop shortcut
python auto_startup.py --desktop-shortcut

# 4. Reboot to test auto-startup (optional)
```

### Method 3: Standalone Executable

```bash
# 1. Build executable
python build_executable.py

# 2. Install using the installer
cd dist
install.bat

# 3. Run from desktop shortcut or start menu
```

## 📁 File Structure

```
pos_system/
├── desktop_launcher.py          # Main desktop application launcher
├── system_tray.py              # System tray integration
├── auto_startup.py             # Auto-startup configuration
├── build_executable.py         # Executable builder
├── desktop_app_settings.py     # Desktop-specific Django settings
├── requirements_desktop.txt    # Desktop dependencies
├── README_DESKTOP.md           # This file
├── logs/                       # Application logs
├── backups/                    # Automatic backups
├── dist/                       # Built executables
└── build/                      # Build artifacts
```

## ⚙️ Configuration

### Desktop Settings

Edit `desktop_app_settings.py` to customize:

```python
POS_DESKTOP_SETTINGS = {
    'AUTO_BACKUP': True,                # Enable automatic backups
    'BACKUP_INTERVAL_HOURS': 24,        # Backup every 24 hours
    'MAX_BACKUP_FILES': 7,              # Keep 7 backup files
    'ENABLE_SYSTEM_TRAY': True,         # Enable system tray
    'AUTO_START_SERVER': True,          # Auto-start server
    'DEFAULT_PORT': 8000,               # Default port
    'BROWSER_AUTO_OPEN': True,          # Auto-open browser
    'MINIMIZE_TO_TRAY': True,           # Minimize to tray
}
```

### Auto-Startup Options

```bash
# Enable auto-startup
python auto_startup.py --enable

# Disable auto-startup
python auto_startup.py --disable

# Check current status
python auto_startup.py --status

# Create desktop shortcut
python auto_startup.py --desktop-shortcut
```

## 🎮 Usage

### System Tray Controls

Right-click the system tray icon for options:
- **Start POS** - Start the POS system
- **Stop POS** - Stop the POS system
- **Open POS** - Open in browser
- **Restart POS** - Restart the system
- **Status** - Show current status
- **Open Logs** - View log files
- **Exit** - Close application

### Desktop Launcher

Double-click the desktop shortcut or run:
```bash
python desktop_launcher.py
```

The application will:
1. Check dependencies
2. Setup Django environment
3. Run migrations
4. Collect static files
5. Start the server
6. Open browser automatically

## 🔧 Troubleshooting

### Common Issues

1. **Port Already in Use**
   - The application automatically finds available ports
   - Check logs for the actual port being used

2. **Dependencies Missing**
   ```bash
   pip install -r requirements_desktop.txt
   ```

3. **Auto-Startup Not Working**
   ```bash
   # Check status
   python auto_startup.py --status
   
   # Re-enable if needed
   python auto_startup.py --disable
   python auto_startup.py --enable
   ```

4. **System Tray Icon Not Showing**
   - Ensure `pystray` is installed: `pip install pystray`
   - Check Windows notification area settings

### Log Files

Check logs for detailed information:
- `logs/pos_desktop_YYYYMMDD.log` - Daily application logs
- `logs/pos_desktop.log` - General application logs

### Reset Application

```bash
# Stop all processes
python auto_startup.py --disable

# Clean build files
python build_executable.py --clean

# Restart setup
python auto_startup.py --enable
```

## 🔒 Security Notes

- The desktop application runs on localhost only
- Database is SQLite for simplicity
- Debug mode is disabled in desktop settings
- Logs are stored locally for privacy

## 📦 Distribution

### For End Users

1. **Build the executable**:
   ```bash
   python build_executable.py
   ```

2. **Share the installer**:
   - Zip the `dist/` folder
   - Share with end users
   - Users run `install.bat` as administrator

### For Developers

1. **Share the source code** with `requirements_desktop.txt`
2. **Users run**:
   ```bash
   pip install -r requirements_desktop.txt
   python auto_startup.py --enable
   ```

## 🆘 Support

### Getting Help

1. **Check logs** in the `logs/` directory
2. **Run with verbose output**:
   ```bash
   python desktop_launcher.py --verbose
   ```
3. **Test individual components**:
   ```bash
   python system_tray.py
   python auto_startup.py --status
   ```

### Reporting Issues

Include the following information:
- Operating System version
- Python version
- Error messages from logs
- Steps to reproduce the issue

## 🎉 Success!

Your Django POS system is now a fully functional desktop application! 

- ✅ Runs automatically on startup
- ✅ Accessible from system tray
- ✅ No manual server management required
- ✅ Professional desktop application experience

Enjoy your automated POS system! 🚀
