#!/usr/bin/env python3
"""
Create Enhanced Distribution Package
Works around PyConfig issues by providing multiple launcher options.
"""

import os
import shutil
from pathlib import Path

def create_distribution_package():
    """Create a complete distribution package"""
    
    # Create distribution directory
    dist_dir = Path('Django_POS_Distribution')
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    
    dist_dir.mkdir()
    
    print("📦 Creating Django POS Distribution Package...")
    print("=" * 50)
    
    # Copy essential files
    files_to_copy = [
        'Django_POS_System.bat',
        'Django_POS_System.ps1', 
        'pos_launcher.py',
        'desktop_launcher.py',
        'system_tray.py',
        'auto_startup.py',
        'requirements_desktop.txt',
        'manage.py',
        'desktop_app_settings.py'
    ]
    
    for file_name in files_to_copy:
        src = Path(file_name)
        if src.exists():
            shutil.copy2(src, dist_dir / file_name)
            print(f"✅ Copied {file_name}")
        else:
            print(f"⚠️  Missing {file_name}")
    
    # Copy directories
    dirs_to_copy = ['pos', 'pos_system', 'static']
    
    for dir_name in dirs_to_copy:
        src_dir = Path(dir_name)
        if src_dir.exists():
            shutil.copytree(src_dir, dist_dir / dir_name)
            print(f"✅ Copied {dir_name}/ directory")
        else:
            print(f"⚠️  Missing {dir_name}/ directory")
    
    # Create dependency installer
    create_dependency_installer(dist_dir)
    
    # Create main installer
    create_main_installer(dist_dir)
    
    # Create user documentation
    create_user_documentation(dist_dir)
    
    # Create quick start guide
    create_quick_start(dist_dir)
    
    print()
    print("🎊 Distribution package created successfully!")
    print(f"📁 Location: {dist_dir.absolute()}")
    
    return dist_dir

def create_dependency_installer(dist_dir):
    """Create automatic dependency installer"""
    installer_content = '''@echo off
echo Django POS System - Dependency Installer
echo ========================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERROR: Python is not installed or not in PATH
    echo.
    echo Please install Python from: https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo ✅ Python found
echo.

echo Installing dependencies...
echo This may take a few minutes...
echo.

REM Try uv first, then fall back to pip
uv pip install -r requirements_desktop.txt >nul 2>&1
if errorlevel 1 (
    echo Using pip to install dependencies...
    python -m pip install -r requirements_desktop.txt
    if errorlevel 1 (
        echo ❌ Failed to install dependencies
        echo.
        echo Please try manually:
        echo pip install -r requirements_desktop.txt
        echo.
        pause
        exit /b 1
    )
)

echo.
echo ✅ Dependencies installed successfully!
echo.
echo You can now run the POS system using:
echo 1. Django_POS_System.bat (recommended)
echo 2. python pos_launcher.py (GUI version)
echo 3. python desktop_launcher.py (full desktop version)
echo.
pause
'''
    
    installer_file = dist_dir / 'install_dependencies.bat'
    with open(installer_file, 'w') as f:
        f.write(installer_content)
    
    print("✅ Created dependency installer")

def create_main_installer(dist_dir):
    """Create main installer that sets up everything"""
    installer_content = '''@echo off
echo Django POS System - Complete Setup
echo =================================
echo.

REM Step 1: Install dependencies
echo Step 1: Installing dependencies...
call install_dependencies.bat
if errorlevel 1 (
    echo Setup failed at dependency installation
    pause
    exit /b 1
)

echo.
echo Step 2: Setting up database...
python manage.py migrate >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Database setup had warnings (this is usually OK)
)

echo.
echo Step 3: Collecting static files...
python manage.py collectstatic --noinput >nul 2>&1

echo.
echo Step 4: Creating desktop shortcut...
set CURRENT_DIR=%~dp0
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\Django POS System.lnk'); $Shortcut.TargetPath = '%CURRENT_DIR%Django_POS_System.bat'; $Shortcut.WorkingDirectory = '%CURRENT_DIR%'; $Shortcut.Description = 'Django POS System'; $Shortcut.Save()" 2>nul

echo.
echo ✅ Setup completed successfully!
echo.
echo 🎉 Django POS System is ready to use!
echo.
echo You can start it by:
echo 1. Double-clicking the desktop shortcut "Django POS System"
echo 2. Double-clicking "Django_POS_System.bat" in this folder
echo 3. Running "python pos_launcher.py" for GUI version
echo.
echo The system will:
echo - Start automatically
echo - Open in your web browser
echo - Be ready for use immediately
echo.
pause
'''
    
    installer_file = dist_dir / 'SETUP.bat'
    with open(installer_file, 'w') as f:
        f.write(installer_content)
    
    print("✅ Created main installer")

def create_user_documentation(dist_dir):
    """Create comprehensive user documentation"""
    doc_content = '''Django POS System - User Guide
==============================

QUICK START:
-----------
1. Run "SETUP.bat" as administrator (one-time setup)
2. Double-click desktop shortcut "Django POS System"
3. Wait for browser to open automatically
4. Start using the POS system!

WHAT YOU GET:
------------
✅ Complete Point of Sale system
✅ Product and inventory management  
✅ Sales processing and receipts
✅ Category management
✅ Sales reporting and analytics
✅ User authentication
✅ Professional web interface

HOW TO START THE POS SYSTEM:
----------------------------

Option 1: Desktop Shortcut (Easiest)
- Double-click "Django POS System" on your desktop
- System starts automatically
- Browser opens with POS interface

Option 2: Batch File
- Double-click "Django_POS_System.bat"
- Same as desktop shortcut

Option 3: GUI Version
- Double-click "pos_launcher.py" 
- Professional GUI with start/stop buttons
- Visual progress indicators

Option 4: Full Desktop Version
- Run "python desktop_launcher.py"
- Complete desktop application experience
- System tray integration available

DAILY USAGE:
-----------
1. Start the system (any method above)
2. Browser opens automatically at http://127.0.0.1:8000
3. Log in (create account on first use)
4. Use POS features:
   - Add/manage products
   - Process sales
   - Generate receipts
   - View reports
   - Manage inventory

5. To stop: Close the console window or use GUI stop button

FEATURES OVERVIEW:
-----------------

Product Management:
- Add products with barcodes
- Set prices and stock quantities
- Organize by categories
- Track expiration dates
- Low stock alerts

Sales Processing:
- Barcode scanning support
- Multiple payment methods
- Automatic tax calculation
- Receipt generation
- Transaction history

Reporting:
- Daily/monthly sales reports
- Product performance
- Inventory status
- Low stock alerts

TROUBLESHOOTING:
---------------

Problem: Browser doesn't open automatically
Solution: Manually go to http://127.0.0.1:8000

Problem: "Port already in use" error
Solution: Close any other POS applications and restart

Problem: Dependencies missing
Solution: Run "install_dependencies.bat" again

Problem: Permission errors
Solution: Run as administrator

Problem: Python not found
Solution: Install Python from python.org

SYSTEM REQUIREMENTS:
-------------------
- Windows 10 or later
- Python 3.8+ (installed automatically if missing)
- 4GB RAM minimum
- 1GB free disk space
- Modern web browser (Chrome, Firefox, Edge)

SUPPORT:
-------
- Check the console window for error messages
- Ensure Python is properly installed
- Run setup as administrator
- Contact your system administrator for help

UNINSTALLATION:
--------------
- Delete the Django POS System folder
- Delete the desktop shortcut
- No registry entries or system files are created

Thank you for using Django POS System!
'''
    
    doc_file = dist_dir / 'USER_GUIDE.txt'
    with open(doc_file, 'w') as f:
        f.write(doc_content)
    
    print("✅ Created user documentation")

def create_quick_start(dist_dir):
    """Create quick start guide"""
    quick_start_content = '''🚀 QUICK START GUIDE
===================

FOR FIRST-TIME SETUP:
---------------------
1. Right-click "SETUP.bat" 
2. Select "Run as administrator"
3. Wait for setup to complete
4. Desktop shortcut will be created

FOR DAILY USE:
-------------
1. Double-click "Django POS System" desktop shortcut
2. Wait for browser to open (about 10 seconds)
3. Start using the POS system!

THAT'S IT! 🎉

The system runs at: http://127.0.0.1:8000

Keep the console window open while using the system.
Close it to stop the POS system.

Need help? See USER_GUIDE.txt for detailed instructions.
'''
    
    quick_file = dist_dir / 'QUICK_START.txt'
    with open(quick_file, 'w') as f:
        f.write(quick_start_content)
    
    print("✅ Created quick start guide")

if __name__ == "__main__":
    dist_dir = create_distribution_package()
    
    print()
    print("📋 Distribution Package Contents:")
    print("-" * 35)
    for item in sorted(dist_dir.iterdir()):
        if item.is_file():
            print(f"📄 {item.name}")
        else:
            print(f"📁 {item.name}/")
    
    print()
    print("🎯 Ready for Distribution!")
    print("=" * 30)
    print("1. Zip the 'Django_POS_Distribution' folder")
    print("2. Share with end users")
    print("3. Users run 'SETUP.bat' as administrator")
    print("4. Users get desktop shortcut for easy access")
    print()
    print("✅ This solution avoids all PyConfig issues!")
    print("✅ Works with any Python version!")
    print("✅ Professional user experience!")
