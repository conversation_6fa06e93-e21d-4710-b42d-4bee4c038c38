Django POS System - User Guide
==============================

QUICK START:
-----------
1. Run "SETUP.bat" as administrator (one-time setup)
2. Double-click desktop shortcut "Django POS System"
3. Wait for browser to open automatically
4. Start using the POS system!

WHAT YOU GET:
------------
- Complete Point of Sale system
- Product and inventory management  
- Sales processing and receipts
- Category management
- Sales reporting and analytics
- User authentication
- Professional web interface

HOW TO START THE POS SYSTEM:
----------------------------

Option 1: Desktop Shortcut (Easiest)
- Double-click "Django POS System" on your desktop
- System starts automatically
- <PERSON>rowser opens with POS interface

Option 2: Batch File
- Double-click "Django_POS_System.bat"
- Same as desktop shortcut

Option 3: GUI Version
- Double-click "pos_launcher.py" 
- Professional GUI with start/stop buttons
- Visual progress indicators

Option 4: Full Desktop Version
- Run "python desktop_launcher.py"
- Complete desktop application experience
- System tray integration available

DAILY USAGE:
-----------
1. Start the system (any method above)
2. <PERSON><PERSON><PERSON> opens automatically at http://127.0.0.1:8000
3. Log in (create account on first use)
4. Use POS features:
   - Add/manage products
   - Process sales
   - Generate receipts
   - View reports
   - Manage inventory

5. To stop: Close the console window or use GUI stop button

FEATURES OVERVIEW:
-----------------

Product Management:
- Add products with barcodes
- Set prices and stock quantities
- Organize by categories
- Track expiration dates
- Low stock alerts

Sales Processing:
- Barcode scanning support
- Multiple payment methods
- Automatic tax calculation
- Receipt generation
- Transaction history

Reporting:
- Daily/monthly sales reports
- Product performance
- Inventory status
- Low stock alerts

TROUBLESHOOTING:
---------------

Problem: Browser doesn't open automatically
Solution: Manually go to http://127.0.0.1:8000

Problem: "Port already in use" error
Solution: Close any other POS applications and restart

Problem: Dependencies missing
Solution: Run "install_dependencies.bat" again

Problem: Permission errors
Solution: Run as administrator

Problem: Python not found
Solution: Install Python from python.org

SYSTEM REQUIREMENTS:
-------------------
- Windows 10 or later
- Python 3.8+ (installed automatically if missing)
- 4GB RAM minimum
- 1GB free disk space
- Modern web browser (Chrome, Firefox, Edge)

SUPPORT:
-------
- Check the console window for error messages
- Ensure Python is properly installed
- Run setup as administrator
- Contact your system administrator for help

UNINSTALLATION:
--------------
- Delete the Django POS System folder
- Delete the desktop shortcut
- No registry entries or system files are created

Thank you for using Django POS System!
