#!/usr/bin/env python3
"""
Fixed Executable Builder for Django POS System
Addresses PyConfig structure issues and Python version compatibility.
"""

import os
import sys
import subprocess
from pathlib import Path
import shutil

def create_simple_launcher():
    """Create a simpler launcher that avoids PyConfig issues"""
    launcher_content = '''#!/usr/bin/env python3
"""
Simple Django POS Launcher - Avoids PyConfig issues
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def main():
    """Simple main function"""
    print("Starting Django POS System...")
    
    # Set up environment
    project_dir = Path(__file__).resolve().parent
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'desktop_app_settings')
    
    try:
        # Start Django server directly
        print("Starting Django server...")
        server_process = subprocess.Popen([
            sys.executable, 'manage.py', 'runserver', '127.0.0.1:8000', '--noreload'
        ], cwd=project_dir)
        
        # Wait a moment for server to start
        time.sleep(3)
        
        # Open browser
        print("Opening browser...")
        webbrowser.open('http://127.0.0.1:8000')
        
        print("POS System is running at http://127.0.0.1:8000")
        print("Press Ctrl+C to stop...")
        
        # Wait for user to stop
        try:
            server_process.wait()
        except KeyboardInterrupt:
            print("Stopping server...")
            server_process.terminate()
            
    except Exception as e:
        print(f"Error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
'''
    
    with open('simple_launcher.py', 'w') as f:
        f.write(launcher_content)
    
    print("✅ Created simple launcher")

def build_with_console():
    """Build executable with console window to avoid PyConfig issues"""
    project_dir = Path(__file__).resolve().parent
    
    print("🔨 Building Console-Based Executable...")
    print("=" * 50)
    
    # Clean previous builds
    build_dir = project_dir / 'build'
    dist_dir = project_dir / 'dist'
    
    if build_dir.exists():
        shutil.rmtree(build_dir)
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    
    # Create simple launcher
    create_simple_launcher()
    
    # PyInstaller command for console app (more reliable)
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',                    # Single file
        '--console',                    # Console window (more reliable than windowed)
        '--name=Django_POS_System',     # Name
        '--add-data=pos;pos',           # Include pos app
        '--add-data=pos_system;pos_system',  # Include settings
        '--add-data=static;static',     # Include static files
        '--add-data=manage.py;.',       # Include manage.py
        '--add-data=desktop_app_settings.py;.',  # Include desktop settings
        '--hidden-import=django',
        '--hidden-import=django.core.management',
        '--hidden-import=django.core.management.commands',
        '--hidden-import=django.core.management.commands.runserver',
        '--hidden-import=pos',
        '--hidden-import=pos.models',
        '--hidden-import=pos.views',
        '--hidden-import=pos.urls',
        '--collect-all=django',         # Collect all Django files
        'simple_launcher.py'
    ]
    
    print("🚀 Running PyInstaller...")
    
    try:
        result = subprocess.run(cmd, cwd=project_dir, check=True)
        print("✅ Build completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        return False

def create_batch_wrapper():
    """Create a batch file that hides the console window"""
    batch_content = '''@echo off
title Django POS System

REM Hide this console window and start the executable
if not DEFINED IS_MINIMIZED set IS_MINIMIZED=1 && start "" /min "%~dpnx0" %* && exit

REM Start the actual executable
"%~dp0Django_POS_System.exe"

REM Keep window open if there's an error
if errorlevel 1 (
    echo.
    echo An error occurred. Press any key to exit...
    pause >nul
)
'''
    
    dist_dir = Path('dist')
    batch_file = dist_dir / 'Start_POS_System.bat'
    
    try:
        with open(batch_file, 'w') as f:
            f.write(batch_content)
        print(f"✅ Created batch wrapper: {batch_file}")
        return True
    except Exception as e:
        print(f"❌ Failed to create batch wrapper: {e}")
        return False

def create_vbs_launcher():
    """Create a VBS launcher that runs silently"""
    vbs_content = '''Set WshShell = CreateObject("WScript.Shell")
WshShell.Run chr(34) & WScript.ScriptFullName & "\..\Django_POS_System.exe" & Chr(34), 0
Set WshShell = Nothing
'''
    
    dist_dir = Path('dist')
    vbs_file = dist_dir / 'Django_POS_System_Silent.vbs'
    
    try:
        with open(vbs_file, 'w') as f:
            f.write(vbs_content)
        print(f"✅ Created VBS launcher: {vbs_file}")
        return True
    except Exception as e:
        print(f"❌ Failed to create VBS launcher: {e}")
        return False

def create_improved_installer():
    """Create an improved installer"""
    installer_content = '''@echo off
echo Django POS System - Improved Installer
echo =====================================
echo.

set INSTALL_DIR=%USERPROFILE%\\Django_POS_System

echo Installing to: %INSTALL_DIR%
echo.

REM Create installation directory
if exist "%INSTALL_DIR%" (
    echo Updating existing installation...
    rmdir /s /q "%INSTALL_DIR%"
)

mkdir "%INSTALL_DIR%"

REM Copy files
echo Copying application files...
copy "Django_POS_System.exe" "%INSTALL_DIR%\\" >nul
copy "Start_POS_System.bat" "%INSTALL_DIR%\\" >nul 2>nul
copy "Django_POS_System_Silent.vbs" "%INSTALL_DIR%\\" >nul 2>nul
copy "README.txt" "%INSTALL_DIR%\\" >nul 2>nul

REM Create desktop shortcuts
echo Creating desktop shortcuts...

REM Shortcut for console version
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\Django POS System.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\Django_POS_System.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Django POS System'; $Shortcut.Save()" >nul 2>nul

REM Shortcut for silent version (if VBS exists)
if exist "%INSTALL_DIR%\\Django_POS_System_Silent.vbs" (
    powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\Django POS System (Silent).lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\Django_POS_System_Silent.vbs'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'Django POS System (Silent Mode)'; $Shortcut.Save()" >nul 2>nul
)

echo.
echo Installation completed successfully!
echo.
echo You now have:
echo 1. Django POS System - Regular version with console
echo 2. Django POS System (Silent) - Runs without console window
echo.
echo Both shortcuts are on your desktop.
echo.
pause
'''
    
    dist_dir = Path('dist')
    installer_file = dist_dir / 'install_improved.bat'
    
    try:
        with open(installer_file, 'w') as f:
            f.write(installer_content)
        print(f"✅ Created improved installer: {installer_file}")
        return True
    except Exception as e:
        print(f"❌ Failed to create improved installer: {e}")
        return False

def main():
    """Main build process"""
    print("Django POS System - Fixed Executable Builder")
    print("=" * 45)
    print()
    
    # Build console executable (more reliable)
    if build_with_console():
        print()
        print("Creating additional launchers...")
        
        # Create batch wrapper
        create_batch_wrapper()
        
        # Create VBS launcher
        create_vbs_launcher()
        
        # Create improved installer
        create_improved_installer()
        
        print()
        print("🎊 SUCCESS! Fixed executable created!")
        print("=" * 40)
        print()
        print("📦 Distribution files:")
        print("  - Django_POS_System.exe (console version - more reliable)")
        print("  - Start_POS_System.bat (hidden console)")
        print("  - Django_POS_System_Silent.vbs (completely silent)")
        print("  - install_improved.bat (improved installer)")
        print()
        print("👥 For end users:")
        print("  1. Run 'install_improved.bat' as administrator")
        print("  2. Choose between regular or silent desktop shortcuts")
        print("  3. Double-click shortcut to start POS system")
        print()
        print("🔧 Troubleshooting:")
        print("  - Console version is more reliable and shows status")
        print("  - Silent version hides the console window")
        print("  - Both versions work the same way")
        
    else:
        print("❌ Build failed. Please check the errors above.")

if __name__ == "__main__":
    main()
